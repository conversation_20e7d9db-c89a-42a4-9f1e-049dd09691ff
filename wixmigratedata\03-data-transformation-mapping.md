# Data Transformation and Field Mapping Guide

## Overview

This document provides detailed mapping instructions for transforming data from the Wix export files to the new database schema. Each source file is mapped to target tables with specific transformation rules.

## 1. Contact+3.csv → Multiple Tables

### Target Tables: customers, contact_inquiries

#### Field Mappings:

**customers table:**
```
Source Field → Target Field (Transformation)
Name → first_name, last_name (Split on space, handle single names)
Email → email (Lowercase, validate format)
Phone → phone_primary (Normalize to +61 format)
Created Date → created_at (Convert to UTC timestamp)
Updated Date → updated_at (Convert to UTC timestamp)
Owner → notes (Append as "Original Owner: {value}")
```

**contact_inquiries table:**
```
Source Field → Target Field (Transformation)
ID → id (Convert to UUID)
Subject → subject (Trim whitespace)
Message → message (Clean HTML, preserve formatting)
Submission Time → submitted_at (Convert to UTC timestamp)
Email → customer_id (Lookup customer by email, create if not exists)
```

#### Transformation Rules:
1. **Name Splitting**: Split "Name" field on first space for first/last name
2. **Phone Normalization**: Convert all phone formats to +61 Australian standard
3. **Email Validation**: Validate email format, reject invalid entries
4. **Duplicate Handling**: Merge contacts with same email address

## 2. Booking list-5_14_2025.csv → Multiple Tables

### Target Tables: customers, bookings, booking_services, booking_form_responses

#### Field Mappings:

**customers table:**
```
First Name → first_name
Last Name → last_name  
Email → email (Lowercase)
Phone → phone_primary (Normalize)
Client Address → customer_addresses table
Registration Date → created_at
```

**bookings table:**
```
Order Number → order_number
Booking Start Time → start_time (Convert timezone)
Booking End Time → end_time (Convert timezone)
Booking Status → status (Map to enum values)
Payment Status → payment_status (Map to enum values)
Order Total → total_amount (Parse currency)
Service Name → booking_services (Link to services)
Location Address → location_address
Staff Member → staff_member
Group Size → group_size
Duration → calculated from start/end times
```

**booking_form_responses table:**
```
Form Field X → field_name
Form Response X → field_value
(Create separate records for each form field/response pair)
```

#### Status Mapping:
```
Wix Status → New Status
"Confirmed" → "confirmed"
"Pending approval" → "pending_approval"
"Canceled" → "canceled"
"Declined" → "declined"
"Incomplete" → "incomplete"
"Booked" → "confirmed"
```

#### Payment Status Mapping:
```
Wix Status → New Status
"Not Paid" → "not_paid"
"Paid" → "paid"
"" (empty) → "not_paid"
```

## 3. Corporate emails.csv → email_campaigns

#### Field Mappings:
```
Name → customer_id (Lookup/create customer)
Email → email
Time → sent_at (Convert to UTC)
Event → status (Map bounce types)
```

#### Event Status Mapping:
```
"SEND" → "sent"
"HARD_BOUNCE" → "bounced" (bounce_type: "hard_bounce")
"SOFT_BOUNCE" → "bounced" (bounce_type: "soft_bounce")
```

## 4. Revenue Summary Report → invoices

#### Field Mappings:
```
Invoice # → invoice_number
Customer → customer_id (Lookup by name)
Sent date → issue_date (Parse date format)
Total (without tax) → subtotal
Total → total_amount
Currency implied → currency ("AUD")
```

#### Transformation Rules:
1. **Date Parsing**: Convert "Jul 1, 2024" format to DATE
2. **Currency Parsing**: Remove "A$" prefix, convert to decimal
3. **Customer Lookup**: Match customer by name, create if not found

## 5. catalog_products.csv → products, product_images

#### Field Mappings:

**products table:**
```
handleId → handle_id
name → name
description → description (Clean HTML)
sku → sku
price → price (Parse decimal)
cost → cost (Parse decimal)
weight → weight (Parse decimal)
inventory → inventory_quantity
visible → is_visible (Convert boolean)
ribbon → ribbon
collection → collection
```

**product_images table:**
```
productImageUrl → image_url (Split on semicolon for multiple images)
name → alt_text (Use product name as alt text)
```

#### Transformation Rules:
1. **HTML Cleaning**: Strip HTML tags from descriptions, preserve line breaks
2. **Image Processing**: Split semicolon-separated image URLs into separate records
3. **Boolean Conversion**: Convert "true"/"false" strings to boolean values

## 6. contacts .Google csv → customers, customer_addresses, customer_companies

#### Field Mappings:

**customers table:**
```
First Name → first_name
Last Name → last_name
E-mail 1 - Value → email
Phone 1 - Value → phone_primary
Phone 2 - Value → phone_secondary
Birthday → date_of_birth (Parse date)
Created At → created_at
Email subscriber status → email_subscription_status
SMS subscriber status → sms_subscription_status
Square Customer ID → square_customer_id
Total Spend → total_spend
Transaction Count → transaction_count
First Visit → first_visit
Last Visit → last_visit
Notes → notes
```

**customer_addresses table:**
```
Address 1 - Label → address_type (Map to enum)
Address 1 - Street → street_line_1
Address 1 - City → city
Address 1 - Region → state_region
Address 1 - Postal Code → postal_code
Address 1 - Country → country
```

**customer_companies table:**
```
Organization Name → companies.name (Create company record)
Organization Title → position
```

#### Subscription Status Mapping:
```
"Subscribed" → "subscribed"
"Never subscribed" → "never_subscribed"
"Unsubscribed" → "unsubscribed"
"" (empty) → "never_subscribed"
```

## 7. contacts Regular.csv → customers (Merge/Update)

#### Merge Strategy:
1. **Primary Key**: Use email as primary identifier
2. **Conflict Resolution**: Prefer Google contacts data over Regular contacts
3. **Field Updates**: Update missing fields from Regular contacts
4. **Duplicate Handling**: Merge records with same email

## 8. invoices.csv → invoices

#### Field Mappings:
```
Customer → customer_id (Lookup by name)
Invoice number → invoice_number (Clean quotes)
Order number → order_number (Clean quotes)
Invoice status → status (Map to enum)
Date issued → issue_date (Parse date)
Due Date → due_date (Parse date)
Currency → currency
Subtotal → subtotal (Parse decimal)
Discount → discount_amount (Parse decimal)
Taxes → tax_amount (Parse decimal)
Total → total_amount (Parse decimal)
```

#### Status Mapping:
```
"Sent" → "sent"
"Paid" → "paid"
"Overdue" → "overdue"
"Void" → "void"
```

## Data Quality Rules

### 1. Email Validation
- Must contain @ symbol
- Must have valid domain
- Convert to lowercase
- Remove leading/trailing whitespace

### 2. Phone Number Normalization
- Remove all non-numeric characters except +
- Convert Australian numbers to +61 format
- Validate length (10-15 digits)
- Handle international numbers appropriately

### 3. Date/Time Handling
- Convert all dates to UTC
- Handle multiple date formats
- Validate date ranges (no future birth dates)
- Default time to 00:00:00 if not specified

### 4. Currency Handling
- Remove currency symbols (A$, $)
- Convert to decimal with 2 decimal places
- Handle negative values (refunds)
- Default currency to AUD

### 5. Text Cleaning
- Remove HTML tags from descriptions
- Preserve line breaks as \n
- Trim whitespace
- Handle special characters properly

## Error Handling

### 1. Invalid Data
- Log all validation errors
- Skip invalid records with detailed logging
- Provide summary report of skipped records

### 2. Missing References
- Create placeholder records for missing customers
- Log all missing reference lookups
- Provide manual review list

### 3. Duplicate Detection
- Email-based duplicate detection
- Phone number similarity matching
- Manual review for potential duplicates

---

*Document Version: 1.0*
*Last Updated: [Current Date]*
*Status: Ready for Implementation*
