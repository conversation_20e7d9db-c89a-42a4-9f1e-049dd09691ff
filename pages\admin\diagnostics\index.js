import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import styles from '@/styles/admin/DiagnosticsPage.module.css';
import supabase from '@/lib/supabase';

export default function DiagnosticsPage() {
  const [systemStatus, setSystemStatus] = useState({
    database: { status: 'checking', message: 'Checking database connection...' },
    auth: { status: 'checking', message: 'Checking authentication service...' },
    api: { status: 'checking', message: 'Checking API endpoints...' },
    storage: { status: 'checking', message: 'Checking storage service...' },
  });

  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Run diagnostics
  useEffect(() => {
    const runDiagnostics = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get auth token from standardized location
        let authToken = null;
        if (typeof window !== 'undefined' && window.sessionStorage) {
          try {
            const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
            if (cachedToken) {
              const tokenData = JSON.parse(cachedToken);
              if (tokenData && tokenData.token) {
                authToken = tokenData.token;
              }
            }
          } catch (tokenError) {
            console.error('Error getting auth token:', tokenError);
          }
        }

        // Check database connection
        try {
          setSystemStatus(prev => ({
            ...prev,
            database: { status: 'checking', message: 'Testing database connection...' }
          }));

          const dbResponse = await fetch('/api/admin/diagnostics/database', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              ...(authToken ? {
                'Authorization': `Bearer ${authToken}`,
                'X-Auth-Token': authToken
              } : {})
            },
            credentials: 'include'
          });

          if (dbResponse.ok) {
            setSystemStatus(prev => ({
              ...prev,
              database: { status: 'healthy', message: 'Database connection successful' }
            }));
          } else {
            const dbData = await dbResponse.json();
            setSystemStatus(prev => ({
              ...prev,
              database: { status: 'error', message: dbData.message || 'Database connection failed' }
            }));
          }
        } catch (dbError) {
          console.error('Database check error:', dbError);
          setSystemStatus(prev => ({
            ...prev,
            database: { status: 'error', message: dbError.message || 'Database connection failed' }
          }));
        }

        // Check authentication service
        try {
          setSystemStatus(prev => ({
            ...prev,
            auth: { status: 'checking', message: 'Testing authentication service...' }
          }));

          // Use a timeout to prevent hanging
          const authCheckPromise = supabase.auth.refreshSession();
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Authentication check timed out after 5 seconds')), 5000);
          });

          // Race the auth check against the timeout
          const { session, error } = await Promise.race([authCheckPromise, timeoutPromise]);

          if (error) {
            console.warn('Auth check returned error:', error);
            setSystemStatus(prev => ({
              ...prev,
              auth: { status: 'error', message: error.message || 'Authentication service check failed' }
            }));

            // Try direct auth check as fallback
            try {
              const authResponse = await fetch('/api/admin/diagnostics/auth-check', {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                  ...(authToken ? {
                    'Authorization': `Bearer ${authToken}`,
                    'X-Auth-Token': authToken
                  } : {})
                },
                credentials: 'include'
              });

              if (authResponse.ok) {
                setSystemStatus(prev => ({
                  ...prev,
                  auth: { status: 'healthy', message: 'Authentication service is working properly (via direct check)' }
                }));
              }
            } catch (fallbackError) {
              console.error('Auth fallback check error:', fallbackError);
              // Keep the original error state
            }
          } else {
            setSystemStatus(prev => ({
              ...prev,
              auth: { status: 'healthy', message: 'Authentication service is working properly' }
            }));
          }
        } catch (authError) {
          console.error('Auth check error:', authError);
          setSystemStatus(prev => ({
            ...prev,
            auth: { status: 'error', message: authError.message || 'Authentication service check failed' }
          }));

          // Try direct auth check as fallback
          try {
            const authResponse = await fetch('/api/admin/diagnostics/auth-check', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                ...(authToken ? {
                  'Authorization': `Bearer ${authToken}`,
                  'X-Auth-Token': authToken
                } : {})
              },
              credentials: 'include'
            });

            if (authResponse.ok) {
              setSystemStatus(prev => ({
                ...prev,
                auth: { status: 'healthy', message: 'Authentication service is working properly (via direct check)' }
              }));
            }
          } catch (fallbackError) {
            console.error('Auth fallback check error:', fallbackError);
            // Keep the original error state
          }
        }

        // Check API endpoints
        try {
          setSystemStatus(prev => ({
            ...prev,
            api: { status: 'checking', message: 'Testing API endpoints...' }
          }));

          const apiResponse = await fetch('/api/admin/diagnostics/api', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              ...(authToken ? {
                'Authorization': `Bearer ${authToken}`,
                'X-Auth-Token': authToken
              } : {})
            },
            credentials: 'include'
          });

          if (apiResponse.ok) {
            setSystemStatus(prev => ({
              ...prev,
              api: { status: 'healthy', message: 'API endpoints are responding correctly' }
            }));
          } else {
            const apiData = await apiResponse.json();
            setSystemStatus(prev => ({
              ...prev,
              api: { status: 'error', message: apiData.message || 'API endpoints check failed' }
            }));
          }
        } catch (apiError) {
          console.error('API check error:', apiError);
          setSystemStatus(prev => ({
            ...prev,
            api: { status: 'error', message: apiError.message || 'API endpoints check failed' }
          }));
        }

        // Check storage service
        try {
          setSystemStatus(prev => ({
            ...prev,
            storage: { status: 'checking', message: 'Testing storage service...' }
          }));

          const storageResponse = await fetch('/api/admin/diagnostics/storage', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              ...(authToken ? {
                'Authorization': `Bearer ${authToken}`,
                'X-Auth-Token': authToken
              } : {})
            },
            credentials: 'include'
          });

          if (storageResponse.ok) {
            setSystemStatus(prev => ({
              ...prev,
              storage: { status: 'healthy', message: 'Storage service is working properly' }
            }));
          } else {
            const storageData = await storageResponse.json();
            setSystemStatus(prev => ({
              ...prev,
              storage: { status: 'error', message: storageData.message || 'Storage service check failed' }
            }));
          }
        } catch (storageError) {
          console.error('Storage check error:', storageError);
          setSystemStatus(prev => ({
            ...prev,
            storage: { status: 'error', message: storageError.message || 'Storage service check failed' }
          }));
        }

        // Fetch system logs
        try {
          const logsResponse = await fetch('/api/admin/diagnostics/logs', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              ...(authToken ? {
                'Authorization': `Bearer ${authToken}`,
                'X-Auth-Token': authToken
              } : {})
            },
            credentials: 'include'
          });

          if (logsResponse.ok) {
            const logsData = await logsResponse.json();
            setLogs(logsData.logs || []);
          } else {
            console.error('Failed to fetch logs');
          }
        } catch (logsError) {
          console.error('Logs fetch error:', logsError);
        }

      } catch (error) {
        console.error('Diagnostics error:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    runDiagnostics();
  }, [refreshKey]);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="System Diagnostics">
        <div className={styles.diagnosticsPage}>
          <div className={styles.header}>
            <h1>System Diagnostics</h1>
            <button
              className={styles.refreshButton}
              onClick={handleRefresh}
              disabled={loading}
            >
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>

          {error && (
            <div className={styles.errorBox}>
              <p>Error: {error}</p>
            </div>
          )}

          <div className={styles.statusGrid}>
            {Object.entries(systemStatus).map(([key, { status, message }]) => (
              <div
                key={key}
                className={`${styles.statusCard} ${styles[status]}`}
              >
                <h2>{key.charAt(0).toUpperCase() + key.slice(1)}</h2>
                <div className={styles.statusIndicator}>
                  <span className={styles.statusDot}></span>
                  <span className={styles.statusText}>
                    {status === 'healthy' ? 'Healthy' :
                     status === 'error' ? 'Error' : 'Checking...'}
                  </span>
                </div>
                <p>{message}</p>
              </div>
            ))}
          </div>

          <div className={styles.logsSection}>
            <h2>System Logs</h2>
            {logs.length > 0 ? (
              <div className={styles.logsContainer}>
                {logs.map((log, index) => (
                  <div key={index} className={`${styles.logEntry} ${styles[log.level]}`}>
                    <span className={styles.logTimestamp}>{new Date(log.timestamp).toLocaleString()}</span>
                    <span className={styles.logLevel}>{log.level}</span>
                    <span className={styles.logMessage}>{log.message}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p className={styles.noLogs}>No logs available</p>
            )}
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
