import Head from 'next/head'
import styles from '@/styles/Contact.module.css'
import Layout from '@/components/Layout'
import { useState } from 'react'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    phone: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [submitError, setSubmitError] = useState('')

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitError('')

    try {
      // In a real implementation, you would send the form data to your backend
      // For now, we'll just simulate a successful submission
      await new Promise(resolve => setTimeout(resolve, 1000))
      setSubmitSuccess(true)
      setFormData({ name: '', email: '', subject: '', phone: '', message: '' })
    } catch (error) {
      setSubmitError('There was an error submitting your message. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Layout>
      <Head>
        <title>Contact | OceanSoulSparkles</title>
        <meta name="description" content="Contact OceanSoulSparkles for face painting, airbrush body art, and braiding services in Melbourne. Get in touch for bookings and inquiries." />
      </Head>

      <main className={styles.main}>
        <section className={styles.hero}>
          <h1 className={styles.title}>Contact Us</h1>
          <p className={styles.description}>
            Have questions or want to discuss your event? We'd love to hear from you!
          </p>
        </section>

        <section className={styles.contactSection}>
          <div className={styles.contactContainer}>
            <div className={styles.contactInfo}>
              <h2>Get In Touch</h2>
              <p>
                Whether you have questions about our services, want to check availability, or need a custom quote,
                we're here to help. Fill out the form and we'll get back to you as soon as possible.
              </p>

              <div className={styles.contactDetails}>
                <div className={styles.contactItem}>
                  <h3>Email</h3>
                  <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>

                <div className={styles.contactItem}>
                  <h3>Location</h3>
                  <p>Melbourne, Victoria, Australia</p>
                </div>

                <div className={styles.contactItem}>
                  <h3>Follow Us</h3>
                  <div className={styles.socialLinks}>
                    <a href="https://www.instagram.com/oceansoulsparkles" target="_blank" rel="noopener noreferrer" className={styles.socialLink}>
                      Instagram
                    </a>
                    <a href="https://www.facebook.com/OceanSoulSparkles/" target="_blank" rel="noopener noreferrer" className={styles.socialLink}>
                      Facebook
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.formSection}>
              <h2>Send Us a Message</h2>
              {submitSuccess ? (
                <div className={styles.successMessage}>
                  <h3>Thank you for your message!</h3>
                  <p>We'll get back to you as soon as possible.</p>
                  <button
                    className={styles.button}
                    onClick={() => setSubmitSuccess(false)}
                  >
                    Send another message
                  </button>
                </div>
              ) : (
                <form className={styles.contactForm} onSubmit={handleSubmit}>
                  <div className={styles.formGroup}>
                    <input
                      type="text"
                      name="name"
                      placeholder="Name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className={styles.formInput}
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <input
                      type="email"
                      name="email"
                      placeholder="Email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className={styles.formInput}
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <input
                      type="text"
                      name="subject"
                      placeholder="Subject"
                      value={formData.subject}
                      onChange={handleChange}
                      className={styles.formInput}
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <input
                      type="tel"
                      name="phone"
                      placeholder="Phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className={styles.formInput}
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <textarea
                      name="message"
                      placeholder="Message"
                      required
                      value={formData.message}
                      onChange={handleChange}
                      className={styles.formTextarea}
                    ></textarea>
                  </div>
                  {submitError && <p className={styles.errorMessage}>{submitError}</p>}
                  <button
                    type="submit"
                    className={styles.submitButton}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit'}
                  </button>
                </form>
              )}
            </div>
          </div>
        </section>

        <section className={styles.bookingCta}>
          <h2>Ready to Book?</h2>
          <p>If you're ready to book our services for your event, head over to our booking page.</p>
          <a href="/book-online" className={styles.ctaButton}>Book Online</a>
        </section>
      </main>
    </Layout>
  )
}
