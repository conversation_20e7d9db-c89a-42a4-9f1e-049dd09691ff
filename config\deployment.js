/**
 * Deployment Configuration for Ocean Soul Sparkles
 * 
 * This file contains configuration settings related to deployment processes,
 * including webhook URLs, deployment environments, and related settings.
 * 
 * These settings can be used in CI/CD workflows, deployment scripts, or
 * other automation processes.
 */

/**
 * Vercel Deployment Webhook URLs
 * 
 * These webhook URLs can be used to trigger deployments to Vercel directly
 * from external systems like GitHub Actions or other CI/CD tools.
 * 
 * Format: https://api.vercel.com/v1/integrations/deploy/:projectId/:token
 * 
 * Documentation: https://vercel.com/docs/rest-api/endpoints#create-a-new-deployment
 */
const VERCEL_WEBHOOKS = {
  /**
   * Production deployment webhook
   * Triggers a deployment to the production environment
   */
  PRODUCTION: 'https://api.vercel.com/v1/integrations/deploy/prj_jxutxkTDXotGVtNxjeyCgc6V8ZKs/NdQQuIPOwE',
  
  /**
   * Preview deployment webhook (if configured)
   * Triggers a deployment to the preview environment
   */
  PREVIEW: '',
};

/**
 * Deployment Environments
 * 
 * Configuration for different deployment environments
 */
const DEPLOYMENT_ENVIRONMENTS = {
  PRODUCTION: {
    name: 'Production',
    url: 'https://www.oceansoulsparkles.com.au',
    branch: 'main',
  },
  PREVIEW: {
    name: 'Preview',
    url: '',
    branch: 'develop',
  },
};

/**
 * Trigger a deployment to Vercel using the webhook
 * 
 * @param {string} environment - The environment to deploy to ('PRODUCTION' or 'PREVIEW')
 * @returns {Promise<Response>} - The fetch response
 */
async function triggerDeployment(environment = 'PRODUCTION') {
  const webhookUrl = VERCEL_WEBHOOKS[environment];
  
  if (!webhookUrl) {
    throw new Error(`No webhook URL configured for environment: ${environment}`);
  }
  
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    return response;
  } catch (error) {
    console.error('Error triggering deployment:', error);
    throw error;
  }
}

module.exports = {
  VERCEL_WEBHOOKS,
  DEPLOYMENT_ENVIRONMENTS,
  triggerDeployment,
};
