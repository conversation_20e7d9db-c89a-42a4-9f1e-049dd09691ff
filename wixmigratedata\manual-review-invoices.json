[{"invoice_data": {"invoice_number": "1000069", "customer_id": "862f339f-4836-4173-89df-133acda1ea15", "order_id": "5167e36c-7a05-498f-bb29-8a068cbfa700", "amount": 350.0, "currency": "AUD", "issue_date": "2024-09-25", "due_date": "2024-09-25", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: fuzzy_medium (0.71)"}, "customer_name": "<PERSON>", "match_type": "fuzzy_medium", "confidence": 0.7058823529411765, "suggested_customer_id": "862f339f-4836-4173-89df-133acda1ea15"}, {"invoice_data": {"invoice_number": "1000068", "customer_id": "2a7a2e57-dc90-44d6-99df-01bc74fd92b0", "order_id": "6c0e50a3-7fd1-4a01-b9c5-4090fb5f608f", "amount": 512.5, "currency": "AUD", "issue_date": "2024-09-25", "due_date": "2024-10-10", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON>. Match: fuzzy_medium (0.74)"}, "customer_name": "<PERSON><PERSON>", "match_type": "fuzzy_medium", "confidence": 0.7407407407407407, "suggested_customer_id": "2a7a2e57-dc90-44d6-99df-01bc74fd92b0"}, {"invoice_data": {"invoice_number": "1000026", "customer_id": "49515223-e976-46da-960a-df8178845399", "order_id": "c9992ada-2f2d-41dc-a595-64217d9ec99a", "amount": 440.0, "currency": "AUD", "issue_date": "2023-11-17", "due_date": "2023-12-17", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: fuzzy_medium (0.75)"}, "customer_name": "<PERSON>", "match_type": "fuzzy_medium", "confidence": 0.75, "suggested_customer_id": "49515223-e976-46da-960a-df8178845399"}, {"invoice_data": {"invoice_number": "1000025", "customer_id": "49515223-e976-46da-960a-df8178845399", "order_id": "33924f4d-6263-49f0-bf9e-3b2c6b548ef5", "amount": 345.0, "currency": "AUD", "issue_date": "2023-11-17", "due_date": "2023-12-17", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: fuzzy_medium (0.75)"}, "customer_name": "<PERSON>", "match_type": "fuzzy_medium", "confidence": 0.75, "suggested_customer_id": "49515223-e976-46da-960a-df8178845399"}, {"invoice_data": {"invoice_number": "1000011", "customer_id": "a10c6310-b280-4c7c-a426-5937a38505f8", "order_id": "53194a46-6729-4ca8-a48b-e68dab2807e5", "amount": 55.0, "currency": "AUD", "issue_date": "2023-08-27", "due_date": "2023-09-11", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Chelsea <PERSON>. Match: fuzzy_medium (0.72)"}, "customer_name": "Chelsea <PERSON>", "match_type": "fuzzy_medium", "confidence": 0.72, "suggested_customer_id": "a10c6310-b280-4c7c-a426-5937a38505f8"}]