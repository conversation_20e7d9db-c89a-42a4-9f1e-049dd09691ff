#!/usr/bin/env python3
"""
Ocean Soul Sparkles - Data Cleaning Scripts
Addresses critical data quality issues identified in the migration analysis.
"""

import pandas as pd
import re
import hashlib
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataCleaner:
    """Main class for cleaning Ocean Soul Sparkles migration data."""

    def __init__(self, data_directory: str = "wixmigratedata/"):
        self.data_dir = data_directory
        self.cleaned_data = {}
        self.quality_report = {
            'duplicates_found': 0,
            'duplicates_resolved': 0,
            'phones_standardized': 0,
            'emails_validated': 0,
            'financial_discrepancies': 0
        }

    def standardize_phone_number(self, phone: str) -> Optional[str]:
        """
        Standardize phone numbers to +61 Australian format.
        Handles 7 different formats found in the data.
        """
        if pd.isna(phone) or not phone:
            return None

        # Clean the phone number
        phone = str(phone).strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')

        # Remove quotes if present
        phone = phone.replace("'", "").replace('"', '')

        # Handle different formats
        if phone.startswith('+61'):
            # Already in correct format, validate length
            if len(phone) == 12 or len(phone) == 13:
                return phone
        elif phone.startswith('61') and len(phone) >= 11:
            # Missing + prefix
            return '+' + phone
        elif phone.startswith('0') and len(phone) == 10:
            # Australian mobile starting with 0
            return '+61' + phone[1:]
        elif phone.startswith('+1'):
            # US number, keep as is
            return phone
        elif len(phone) == 9 and phone.startswith('4'):
            # Mobile without country code or leading 0
            return '+61' + phone
        elif len(phone) == 8:
            # Landline without area code - flag for manual review
            logger.warning(f"Incomplete phone number requires manual review: {phone}")
            return None

        # If we can't standardize, log for manual review
        logger.warning(f"Unable to standardize phone number: {phone}")
        return None

    def validate_email(self, email: str) -> Tuple[bool, str]:
        """
        Validate email addresses and clean them.
        Returns (is_valid, cleaned_email)
        """
        if pd.isna(email) or not email:
            return False, ""

        email = str(email).strip().lower()

        # Basic email validation regex
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

        if re.match(email_pattern, email):
            return True, email
        else:
            logger.warning(f"Invalid email format: {email}")
            return False, email

    def detect_customer_duplicates(self, contacts_google: pd.DataFrame,
                                 contacts_regular: pd.DataFrame,
                                 contact_form: pd.DataFrame) -> Dict:
        """
        Detect duplicate customers across multiple data sources.
        Uses email as primary key with fuzzy matching on names.
        """
        logger.info("Starting duplicate detection analysis...")

        duplicates = {
            'email_duplicates': [],
            'name_duplicates': [],
            'merge_candidates': []
        }

        # Combine all contact sources
        all_contacts = []

        # Process Google contacts
        if not contacts_google.empty:
            google_contacts = contacts_google[['E-mail 1 - Value', 'First Name', 'Last Name']].copy()
            google_contacts['source'] = 'google'
            google_contacts.columns = ['email', 'first_name', 'last_name', 'source']
            all_contacts.append(google_contacts)

        # Process Regular contacts
        if not contacts_regular.empty:
            regular_contacts = contacts_regular[['Email', 'First Name', 'Last Name']].copy()
            regular_contacts['source'] = 'regular'
            regular_contacts.columns = ['email', 'first_name', 'last_name', 'source']
            all_contacts.append(regular_contacts)

        # Process Contact form submissions
        if not contact_form.empty:
            form_contacts = contact_form[['Email', 'Name']].copy()
            form_contacts['source'] = 'contact_form'
            # Split name into first/last
            form_contacts[['first_name', 'last_name']] = form_contacts['Name'].str.split(' ', 1, expand=True)
            form_contacts = form_contacts[['Email', 'first_name', 'last_name', 'source']]
            form_contacts.columns = ['email', 'first_name', 'last_name', 'source']
            all_contacts.append(form_contacts)

        # Combine all contacts
        combined_contacts = pd.concat(all_contacts, ignore_index=True)
        combined_contacts = combined_contacts.dropna(subset=['email'])

        # Find email duplicates
        email_counts = combined_contacts['email'].value_counts()
        duplicate_emails = email_counts[email_counts > 1].index.tolist()

        for email in duplicate_emails:
            email_records = combined_contacts[combined_contacts['email'] == email]
            duplicates['email_duplicates'].append({
                'email': email,
                'records': email_records.to_dict('records'),
                'count': len(email_records)
            })

        # Find potential name duplicates (same name, different email)
        combined_contacts['full_name'] = (combined_contacts['first_name'].fillna('') + ' ' +
                                        combined_contacts['last_name'].fillna('')).str.strip()

        name_counts = combined_contacts['full_name'].value_counts()
        duplicate_names = name_counts[name_counts > 1].index.tolist()

        for name in duplicate_names:
            if name and name != '':
                name_records = combined_contacts[combined_contacts['full_name'] == name]
                unique_emails = name_records['email'].nunique()
                if unique_emails > 1:
                    duplicates['name_duplicates'].append({
                        'name': name,
                        'records': name_records.to_dict('records'),
                        'unique_emails': unique_emails
                    })

        self.quality_report['duplicates_found'] = len(duplicate_emails) + len(duplicate_names)
        logger.info(f"Found {len(duplicate_emails)} email duplicates and {len(duplicate_names)} name duplicates")

        return duplicates

    def reconcile_financial_data(self, revenue_summary: pd.DataFrame,
                               detailed_invoices: pd.DataFrame) -> Dict:
        """
        Reconcile financial data between revenue summary and detailed invoices.
        """
        logger.info("Starting financial data reconciliation...")

        reconciliation = {
            'revenue_summary_total': 0,
            'detailed_invoices_total': 0,
            'discrepancies': [],
            'missing_invoices': [],
            'duplicate_invoices': []
        }

        # Calculate revenue summary total
        if not revenue_summary.empty and 'Total' in revenue_summary.columns:
            # Clean currency values
            revenue_summary['Total_Clean'] = revenue_summary['Total'].astype(str).str.replace('A$', '').str.replace('$', '').str.replace(',', '')
            revenue_summary['Total_Clean'] = pd.to_numeric(revenue_summary['Total_Clean'], errors='coerce')
            reconciliation['revenue_summary_total'] = revenue_summary['Total_Clean'].sum()

        # Calculate detailed invoices total
        if not detailed_invoices.empty and 'Total' in detailed_invoices.columns:
            # Clean currency values
            detailed_invoices['Total_Clean'] = detailed_invoices['Total'].astype(str).str.replace('A$', '').str.replace('$', '').str.replace(',', '')
            detailed_invoices['Total_Clean'] = pd.to_numeric(detailed_invoices['Total_Clean'], errors='coerce')
            reconciliation['detailed_invoices_total'] = detailed_invoices['Total_Clean'].sum()

        # Calculate discrepancy
        discrepancy = abs(reconciliation['revenue_summary_total'] - reconciliation['detailed_invoices_total'])
        reconciliation['discrepancy_amount'] = discrepancy

        # Find missing invoices (in one dataset but not the other)
        if not revenue_summary.empty and not detailed_invoices.empty:
            if 'Invoice #' in revenue_summary.columns and 'Invoice number' in detailed_invoices.columns:
                revenue_invoices = set(revenue_summary['Invoice #'].dropna().astype(str))
                detailed_invoice_numbers = set(detailed_invoices['Invoice number'].dropna().astype(str))

                missing_in_detailed = revenue_invoices - detailed_invoice_numbers
                missing_in_summary = detailed_invoice_numbers - revenue_invoices

                reconciliation['missing_invoices'] = {
                    'missing_in_detailed': list(missing_in_detailed),
                    'missing_in_summary': list(missing_in_summary)
                }

        self.quality_report['financial_discrepancies'] = len(reconciliation['discrepancies'])
        logger.info(f"Financial reconciliation complete. Discrepancy: ${discrepancy:.2f}")

        return reconciliation

    def clean_booking_data(self, bookings: pd.DataFrame) -> pd.DataFrame:
        """
        Clean booking data and flag incomplete records.
        """
        logger.info("Cleaning booking data...")

        if bookings.empty:
            return bookings

        cleaned_bookings = bookings.copy()

        # Flag incomplete records
        cleaned_bookings['incomplete_customer_name'] = (
            cleaned_bookings['First Name'].isna() |
            cleaned_bookings['Last Name'].isna() |
            (cleaned_bookings['First Name'] == '') |
            (cleaned_bookings['Last Name'] == '')
        )

        cleaned_bookings['incomplete_email'] = (
            cleaned_bookings['Email'].isna() |
            (cleaned_bookings['Email'] == '')
        )

        cleaned_bookings['incomplete_phone'] = (
            cleaned_bookings['Phone'].isna() |
            (cleaned_bookings['Phone'] == '')
        )

        # Count incomplete records
        incomplete_names = cleaned_bookings['incomplete_customer_name'].sum()
        incomplete_emails = cleaned_bookings['incomplete_email'].sum()
        incomplete_phones = cleaned_bookings['incomplete_phone'].sum()

        logger.info(f"Found {incomplete_names} bookings with incomplete names")
        logger.info(f"Found {incomplete_emails} bookings with incomplete emails")
        logger.info(f"Found {incomplete_phones} bookings with incomplete phones")

        return cleaned_bookings

    def generate_cleaning_report(self) -> str:
        """
        Generate a comprehensive data cleaning report.
        """
        report = f"""
# Data Cleaning Report - Ocean Soul Sparkles

## Summary
- Duplicates Found: {self.quality_report['duplicates_found']}
- Duplicates Resolved: {self.quality_report['duplicates_resolved']}
- Phone Numbers Standardized: {self.quality_report['phones_standardized']}
- Emails Validated: {self.quality_report['emails_validated']}
- Financial Discrepancies: {self.quality_report['financial_discrepancies']}

## Recommendations
1. Review duplicate customer records for manual merge decisions
2. Validate standardized phone numbers before import
3. Remove invalid email addresses from marketing lists
4. Investigate financial discrepancies before migration

## Next Steps
1. Execute data cleaning transformations
2. Set up staging database with cleaned data
3. Implement validation rules in new system
4. Proceed with Phase 1 migration
"""
        return report

    def execute_full_cleaning_process(self):
        """Execute the complete data cleaning process for all CSV files."""
        logger.info("Starting comprehensive data cleaning process...")

        # Load all CSV files
        csv_files = {
            'google_contacts': 'contacts .Google csv',
            'regular_contacts': 'contacts Regular.csv',
            'bookings': 'Booking list-5_14_2025.csv',
            'invoices': 'invoices.csv',
            'revenue_summary': 'Revenue Summary Report WIX 1.7.24-14.5.25 .csv',
            'products': 'catalog_products.csv',
            'corporate_emails': 'Corporate emails.csv'
        }

        cleaned_data = {}

        for file_key, filename in csv_files.items():
            try:
                file_path = f"{self.data_dir}{filename}"
                logger.info(f"Processing {filename}...")

                df = pd.read_csv(file_path, encoding='utf-8')
                logger.info(f"Loaded {len(df)} records from {filename}")

                # Apply specific cleaning based on file type
                if file_key in ['google_contacts', 'regular_contacts']:
                    cleaned_df = self.clean_contact_data(df, file_key)
                elif file_key == 'bookings':
                    cleaned_df = self.clean_booking_data(df)
                elif file_key == 'invoices':
                    cleaned_df = self.clean_invoice_data(df)
                elif file_key == 'products':
                    cleaned_df = self.clean_product_data(df)
                elif file_key == 'corporate_emails':
                    cleaned_df = self.clean_email_campaign_data(df)
                else:
                    cleaned_df = df

                cleaned_data[file_key] = cleaned_df
                logger.info(f"Cleaned {filename}: {len(cleaned_df)} records")

            except Exception as e:
                logger.error(f"Error processing {filename}: {str(e)}")
                continue

        # Resolve duplicates across contact sources
        if 'google_contacts' in cleaned_data and 'regular_contacts' in cleaned_data:
            merged_customers = self.resolve_customer_duplicates(
                cleaned_data['google_contacts'],
                cleaned_data['regular_contacts'],
                None  # No contact form data available
            )
            cleaned_data['merged_customers'] = merged_customers

        self.cleaned_data = cleaned_data
        return cleaned_data

    def clean_contact_data(self, df: pd.DataFrame, source: str) -> pd.DataFrame:
        """Clean contact data from Google or Regular contacts."""
        cleaned_df = df.copy()

        # Standardize column names based on source
        if source == 'google_contacts':
            column_mapping = {
                'First Name': 'first_name',
                'Last Name': 'last_name',
                'E-mail 1 - Value': 'email',
                'Phone 1 - Value': 'phone_primary',
                'Phone 2 - Value': 'phone_secondary',
                'Birthday': 'date_of_birth',
                'Email subscriber status': 'email_subscription_status',
                'SMS subscriber status': 'sms_subscription_status',
                'Square Customer ID': 'square_customer_id',
                'Total Spend': 'total_spend',
                'Transaction Count': 'transaction_count',
                'First Visit': 'first_visit',
                'Last Visit': 'last_visit',
                'Notes': 'notes',
                'Organization Name': 'company_name',
                'Organization Title': 'position'
            }
        else:  # regular_contacts
            column_mapping = {
                'First Name': 'first_name',
                'Last Name': 'last_name',
                'Email': 'email',
                'Phone': 'phone_primary',
                'Notes': 'notes'
            }

        # Rename columns
        for old_col, new_col in column_mapping.items():
            if old_col in cleaned_df.columns:
                cleaned_df[new_col] = cleaned_df[old_col]

        # Clean and validate emails
        if 'email' in cleaned_df.columns:
            cleaned_df['email_valid'] = cleaned_df['email'].apply(
                lambda x: self.validate_email(x)[0] if pd.notna(x) else False
            )
            cleaned_df['email_cleaned'] = cleaned_df['email'].apply(
                lambda x: self.validate_email(x)[1] if pd.notna(x) else ''
            )
            self.quality_report['emails_validated'] += len(cleaned_df)

        # Standardize phone numbers
        for phone_col in ['phone_primary', 'phone_secondary']:
            if phone_col in cleaned_df.columns:
                cleaned_df[f'{phone_col}_standardized'] = cleaned_df[phone_col].apply(
                    self.standardize_phone_number
                )
                standardized_count = cleaned_df[f'{phone_col}_standardized'].notna().sum()
                self.quality_report['phones_standardized'] += standardized_count

        # Add source tracking
        cleaned_df['original_source'] = source

        return cleaned_df

    def clean_contact_form_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean contact form submission data."""
        cleaned_df = df.copy()

        # Map columns
        column_mapping = {
            'Name': 'name',
            'Email': 'email',
            'Phone': 'phone_primary',
            'Subject': 'subject',
            'Message': 'message',
            'Submission Time': 'submitted_at',
            'ID': 'original_submission_id'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in cleaned_df.columns:
                cleaned_df[new_col] = cleaned_df[old_col]

        # Split name into first/last
        if 'name' in cleaned_df.columns:
            name_split = cleaned_df['name'].str.split(' ', 1, expand=True)
            cleaned_df['first_name'] = name_split[0] if len(name_split.columns) > 0 else ''
            cleaned_df['last_name'] = name_split[1] if len(name_split.columns) > 1 else ''

        # Clean emails and phones
        if 'email' in cleaned_df.columns:
            cleaned_df['email_cleaned'] = cleaned_df['email'].apply(
                lambda x: self.validate_email(x)[1] if pd.notna(x) else ''
            )

        if 'phone_primary' in cleaned_df.columns:
            cleaned_df['phone_primary_standardized'] = cleaned_df['phone_primary'].apply(
                self.standardize_phone_number
            )

        cleaned_df['original_source'] = 'contact_form'
        return cleaned_df

    def clean_invoice_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean invoice data."""
        cleaned_df = df.copy()

        # Remove Excel formula prefixes and quotes
        for col in cleaned_df.columns:
            if cleaned_df[col].dtype == 'object':
                cleaned_df[col] = cleaned_df[col].astype(str).str.replace('=""', '').str.replace('""', '').str.strip()

        # Map columns
        column_mapping = {
            'Customer': 'customer_name',
            'Invoice number': 'invoice_number',
            'Order number': 'order_number',
            'Invoice status': 'status',
            'Date issued': 'issue_date',
            'Due Date': 'due_date',
            'Currency': 'currency',
            'Subtotal': 'subtotal',
            'Discount': 'discount_amount',
            'Taxes': 'tax_amount',
            'Total': 'total_amount'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in cleaned_df.columns:
                cleaned_df[new_col] = cleaned_df[old_col]

        # Clean currency amounts
        for amount_col in ['subtotal', 'discount_amount', 'tax_amount', 'total_amount']:
            if amount_col in cleaned_df.columns:
                cleaned_df[amount_col] = pd.to_numeric(cleaned_df[amount_col], errors='coerce')

        return cleaned_df

    def clean_product_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean product catalog data."""
        cleaned_df = df.copy()

        # Map columns
        column_mapping = {
            'handleId': 'handle_id',
            'name': 'name',
            'description': 'description',
            'sku': 'sku',
            'price': 'price',
            'cost': 'cost',
            'weight': 'weight',
            'inventory': 'inventory_quantity',
            'visible': 'is_visible',
            'ribbon': 'ribbon',
            'collection': 'collection',
            'productImageUrl': 'image_urls'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in cleaned_df.columns:
                cleaned_df[new_col] = cleaned_df[old_col]

        # Clean numeric fields
        for num_col in ['price', 'cost', 'weight', 'inventory_quantity']:
            if num_col in cleaned_df.columns:
                cleaned_df[num_col] = pd.to_numeric(cleaned_df[num_col], errors='coerce')

        # Clean boolean fields
        if 'is_visible' in cleaned_df.columns:
            cleaned_df['is_visible'] = cleaned_df['is_visible'].map({'true': True, 'false': False})

        return cleaned_df

    def clean_email_campaign_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean corporate email campaign data."""
        cleaned_df = df.copy()

        # Map columns
        column_mapping = {
            'Name': 'customer_name',
            'Email': 'email',
            'Time': 'sent_at',
            'Event': 'status'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in cleaned_df.columns:
                cleaned_df[new_col] = cleaned_df[old_col]

        # Map status values
        status_mapping = {
            'SEND': 'sent',
            'HARD_BOUNCE': 'bounced',
            'SOFT_BOUNCE': 'bounced'
        }

        if 'status' in cleaned_df.columns:
            cleaned_df['bounce_type'] = cleaned_df['status'].map({
                'HARD_BOUNCE': 'hard_bounce',
                'SOFT_BOUNCE': 'soft_bounce'
            })
            cleaned_df['status'] = cleaned_df['status'].map(status_mapping)

        return cleaned_df

    def resolve_customer_duplicates(self, google_df: pd.DataFrame, regular_df: pd.DataFrame,
                                  contact_form_df: pd.DataFrame = None) -> pd.DataFrame:
        """Resolve duplicate customers using business rules."""
        logger.info("Resolving customer duplicates...")

        # Implement specific duplicate resolution for known cases
        merged_customers = []

        # Jessica Endsor case - merge business and personal emails
        jessica_records = []
        for df, source in [(google_df, 'google_contacts'), (regular_df, 'regular_contacts')]:
            if 'first_name' in df.columns and 'last_name' in df.columns:
                jessica_mask = (df['first_name'].str.contains('Jessica', na=False)) & \
                              (df['last_name'].str.contains('Endsor', na=False))
                if jessica_mask.any():
                    jessica_records.extend(df[jessica_mask].to_dict('records'))

        if len(jessica_records) > 1:
            # Use the record with Square customer ID as primary
            primary_record = None
            for record in jessica_records:
                if record.get('square_customer_id'):
                    primary_record = record
                    break

            if primary_record:
                # Add secondary emails to notes
                secondary_emails = [r.get('email') for r in jessica_records
                                  if r.get('email') != primary_record.get('email')]
                if secondary_emails:
                    primary_record['migration_notes'] = f"Secondary emails: {', '.join(secondary_emails)}"
                primary_record['duplicate_resolved'] = True
                merged_customers.append(primary_record)
                self.quality_report['duplicates_resolved'] += 1

        # Continue with other customers (simplified for now)
        # In production, implement full duplicate resolution logic

        return pd.DataFrame(merged_customers)

def main():
    """Main execution function for data cleaning."""
    logger.info("Starting Ocean Soul Sparkles data cleaning process...")

    cleaner = DataCleaner()

    # Execute full cleaning process
    try:
        cleaned_data = cleaner.execute_full_cleaning_process()

        # Generate comprehensive report
        report = cleaner.generate_cleaning_report()

        # Save cleaned data for import
        for file_key, df in cleaned_data.items():
            output_path = f"wixmigratedata/cleaned_{file_key}.csv"
            df.to_csv(output_path, index=False)
            logger.info(f"Saved cleaned data to {output_path}")

        logger.info("Data cleaning process completed successfully")
        return report

    except Exception as e:
        logger.error(f"Data cleaning process failed: {str(e)}")
        return f"Error: {str(e)}"

if __name__ == "__main__":
    report = main()
    print(report)
