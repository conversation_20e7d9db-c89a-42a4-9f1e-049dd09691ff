2025-05-27 13:07:53,083 - INFO - Starting missing customer creation process...
2025-05-27 13:07:53,090 - INFO - Loaded 58 manual review cases
2025-05-27 13:07:53,090 - INFO - Found 41 unique missing customers
2025-05-27 13:07:53,090 - INFO - Creating 41 missing customers...
2025-05-27 13:07:53,587 - ERROR - Failed to create customer Veni May: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:53,677 - ERROR - Failed to create customer Dan<PERSON>: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:53,755 - ERROR - Failed to create customer Kenny Z: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:53,822 - ERROR - Failed to create customer Monbulk College Parents Club: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:53,914 - ERROR - Failed to create customer Nelson Alexander: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:53,985 - ERROR - Failed to create customer Electric Lady Land Matt Ambler: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,053 - ERROR - Failed to create customer MYOB: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,118 - ERROR - Failed to create customer La Trobe University: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,186 - ERROR - Failed to create customer FabFun: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,257 - ERROR - Failed to create customer Luke Priday: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,331 - ERROR - Failed to create customer Riely Saville: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,400 - ERROR - Failed to create customer Antonella Face Painting Melbourne: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,467 - ERROR - Failed to create customer Milan Mili (La Trobe): {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,533 - ERROR - Failed to create customer YMCA / YES Youth Hub: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,600 - ERROR - Failed to create customer Rachel Coppel: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,680 - ERROR - Failed to create customer Abi: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,740 - ERROR - Failed to create customer Danni Patterson: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,818 - ERROR - Failed to create customer Ally Maye: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,903 - ERROR - Failed to create customer Kstar Group ATF: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:54,977 - ERROR - Failed to create customer Claire Willis: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,041 - ERROR - Failed to create customer Patricia Bowlby: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,120 - ERROR - Failed to create customer Rhys Marsh: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,196 - ERROR - Failed to create customer Sub Club Melbourne Jonathan Tiatia: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,267 - ERROR - Failed to create customer Chloe Selleck: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,338 - ERROR - Failed to create customer Rochelle: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,407 - ERROR - Failed to create customer Miriam: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,517 - ERROR - Failed to create customer TJ Pockets: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,595 - ERROR - Failed to create customer Ivanhoe East Primary School: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,669 - ERROR - Failed to create customer MAGIC MOMENTS ENTERTAINMENT PTY LTD. Priyanshu Tomar: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,745 - ERROR - Failed to create customer Dangerous Goods: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,813 - ERROR - Failed to create customer Jatinder Bhatti: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,889 - ERROR - Failed to create customer Casey Vance: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:55,963 - ERROR - Failed to <NAME_EMAIL>: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,036 - ERROR - Failed to create customer Alexander  Vivian-riding: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,108 - ERROR - Failed to create customer Emma Reid: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,178 - ERROR - Failed to create customer Balloonaversal Entertainments Peter Patterson: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,247 - ERROR - Failed to create customer La trobe: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,325 - ERROR - Failed to create customer Officeworks Accounts Payable: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,397 - ERROR - Failed to create customer Clarissa PaintNSparkles: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,467 - ERROR - Failed to create customer Eat The Beat: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,536 - ERROR - Failed to create customer Frosty Solana Collective: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_type' column of 'customers' in the schema cache"}
2025-05-27 13:07:56,538 - INFO - Generated customer creation report
2025-05-27 13:07:56,539 - INFO - Missing customer creation completed successfully!
2025-05-27 13:45:16,899 - INFO - Starting missing customer creation process...
2025-05-27 13:45:16,907 - INFO - Loaded 58 manual review cases
2025-05-27 13:45:16,908 - INFO - Found 41 unique missing customers
2025-05-27 13:45:16,908 - INFO - Creating 41 missing customers...
2025-05-27 13:45:17,547 - ERROR - Failed to create customer YMCA / YES Youth Hub: {"code":"23502","details":"Failing row contains (984ebb0c-d1bd-4d9b-8910-1ea627a24de9, YMCA / YES Youth Hub, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:13.275871+00, 2025-05-27 03:45:13.275871+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:13.275871+00, null, YMCA, / YES Youth Hub, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:17,705 - ERROR - Failed to create customer Luke Priday: {"code":"23502","details":"Failing row contains (89c5de57-ed98-4389-aada-29f64e505b42, Luke Priday, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:13.460208+00, 2025-05-27 03:45:13.460208+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:13.460208+00, null, Luke, Priday, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:17,834 - ERROR - Failed to create customer Rachel Coppel: {"code":"23502","details":"Failing row contains (3956b5a5-0866-4698-8fea-a161cace107a, Rachel Coppel, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:13.614818+00, 2025-05-27 03:45:13.614818+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:13.614818+00, null, Rachel, Coppel, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:17,962 - ERROR - Failed to create customer Abi: {"code":"23502","details":"Failing row contains (3667a6f0-4514-49c3-8700-ad0136e85c44, Abi, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:13.722226+00, 2025-05-27 03:45:13.722226+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:13.722226+00, null, Abi, , null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,080 - ERROR - Failed to create customer Casey Vance: {"code":"23502","details":"Failing row contains (9f412df4-16ea-4b1d-a197-264fe94a7f0f, Casey Vance, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:13.862849+00, 2025-05-27 03:45:13.862849+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:13.862849+00, null, Casey, Vance, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,197 - ERROR - Failed to create customer Chloe Selleck: {"code":"23502","details":"Failing row contains (773659fa-7e2d-4b99-9573-53f3650a0e57, Chloe Selleck, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:13.969014+00, 2025-05-27 03:45:13.969014+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:13.969014+00, null, Chloe, Selleck, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,300 - ERROR - Failed to create customer Ally Maye: {"code":"23502","details":"Failing row contains (5c2b6089-a5cf-4615-b40c-184e629ed17c, Ally Maye, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.078104+00, 2025-05-27 03:45:14.078104+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.078104+00, null, Ally, Maye, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,393 - ERROR - Failed to create customer Nelson Alexander: {"code":"23502","details":"Failing row contains (bcf737f3-bb0d-44b3-892a-37f32d1e3587, Nelson Alexander, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.176524+00, 2025-05-27 03:45:14.176524+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.176524+00, null, Nelson, Alexander, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,467 - ERROR - Failed to create customer Electric Lady Land Matt Ambler: {"code":"23502","details":"Failing row contains (ec740694-d5c4-4bf8-a950-1dc073c681ed, Electric Lady Land Matt Ambler, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.250627+00, 2025-05-27 03:45:14.250627+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.250627+00, null, Electric, Lady Land Matt Ambler, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,633 - ERROR - Failed to create customer Rochelle: {"code":"23502","details":"Failing row contains (5fcf391b-b247-48e0-99e2-82e76a172bb1, Rochelle, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.35215+00, 2025-05-27 03:45:14.35215+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.35215+00, null, Rochelle, , null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,718 - ERROR - Failed to create customer TJ Pockets: {"code":"23502","details":"Failing row contains (4abc33b4-8f43-4758-a9d1-a2ceeb66f0fb, TJ Pockets, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.484415+00, 2025-05-27 03:45:14.484415+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.484415+00, null, TJ, Pockets, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,801 - ERROR - Failed to create customer Monbulk College Parents Club: {"code":"23502","details":"Failing row contains (5faca86f-5ac7-4d19-9e93-fa7ce1f38bd6, Monbulk College Parents Club, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:14.572054+00, 2025-05-27 03:45:14.572054+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.572054+00, null, Monbulk, College Parents Club, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,880 - ERROR - Failed to create customer Officeworks Accounts Payable: {"code":"23502","details":"Failing row contains (87ab9297-c8fb-479c-bed2-24f6697e2940, Officeworks Accounts Payable, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.662691+00, 2025-05-27 03:45:14.662691+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.662691+00, null, Officeworks, Accounts Payable, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:18,956 - ERROR - Failed to create customer Kenny Z: {"code":"23502","details":"Failing row contains (4677d019-7726-4cde-8eff-8c0545b5583b, Kenny Z, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.738194+00, 2025-05-27 03:45:14.738194+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.738194+00, null, Kenny, Z, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,035 - ERROR - Failed to create customer Danni Patterson: {"code":"23502","details":"Failing row contains (6d0be56a-d94d-4087-ae81-09b9bbdcf2c6, Danni Patterson, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.816844+00, 2025-05-27 03:45:14.816844+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.816844+00, null, Danni, Patterson, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,119 - ERROR - Failed to create customer Miriam: {"code":"23502","details":"Failing row contains (9fd8334d-8605-4cc9-b440-573a4f49ec9c, Miriam, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:14.888824+00, 2025-05-27 03:45:14.888824+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.888824+00, null, Miriam, , null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,189 - ERROR - Failed to create customer Sub Club Melbourne Jonathan Tiatia: {"code":"23502","details":"Failing row contains (35d88e31-726c-4def-8e0e-5a7d194916e1, Sub Club Melbourne Jonathan Tiatia, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:14.971047+00, 2025-05-27 03:45:14.971047+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:14.971047+00, null, Sub, Club Melbourne Jonathan Tiatia, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,299 - ERROR - Failed to create customer Jatinder Bhatti: {"code":"23502","details":"Failing row contains (29919581-7269-447b-aa22-79b33e117f05, Jatinder Bhatti, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.080971+00, 2025-05-27 03:45:15.080971+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.080971+00, null, Jatinder, Bhatti, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,375 - ERROR - Failed to create customer Patricia Bowlby: {"code":"23502","details":"Failing row contains (c5d0fb1d-de6d-427e-8140-be226bef440a, Patricia Bowlby, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.159497+00, 2025-05-27 03:45:15.159497+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.159497+00, null, Patricia, Bowlby, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,459 - ERROR - Failed to create customer MAGIC MOMENTS ENTERTAINMENT PTY LTD. Priyanshu Tomar: {"code":"23502","details":"Failing row contains (576913ba-a419-44e5-b918-f5a01cbfd26d, MAGIC MOMENTS ENTERTAINMENT PTY LTD. Priyanshu Tomar, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:15.232246+00, 2025-05-27 03:45:15.232246+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.232246+00, null, MAGIC, MOMENTS ENTERTAINMENT PTY LTD. Priyanshu Tomar, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,536 - ERROR - Failed to create customer MYOB: {"code":"23502","details":"Failing row contains (257e71fc-cb7b-4f16-a0ce-6082996c3d26, MYOB, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.317886+00, 2025-05-27 03:45:15.317886+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.317886+00, null, MYOB, , null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,619 - ERROR - Failed to create customer Clarissa PaintNSparkles: {"code":"23502","details":"Failing row contains (d41c4b8e-971c-443d-9af1-e74fa7d61acc, Clarissa PaintNSparkles, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.399607+00, 2025-05-27 03:45:15.399607+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.399607+00, null, Clarissa, PaintNSparkles, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,725 - ERROR - Failed to create customer Ivanhoe East Primary School: {"code":"23502","details":"Failing row contains (499744ff-4fde-49e5-9b74-ae55f212bb85, Ivanhoe East Primary School, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:15.508365+00, 2025-05-27 03:45:15.508365+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.508365+00, null, Ivanhoe, East Primary School, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,814 - ERROR - Failed to create customer Antonella Face Painting Melbourne: {"code":"23502","details":"Failing row contains (26df697e-9791-49bc-9c89-7adde5e3c813, Antonella Face Painting Melbourne, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.5878+00, 2025-05-27 03:45:15.5878+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.5878+00, null, Antonella, Face Painting Melbourne, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,892 - ERROR - Failed to create customer Milan Mili (La Trobe): {"code":"23502","details":"Failing row contains (618cb58d-6be3-47e9-a258-5f25ca5a7f21, Milan Mili (La Trobe), null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.674046+00, 2025-05-27 03:45:15.674046+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.674046+00, null, Milan, Mili (La Trobe), null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:19,964 - ERROR - Failed to create customer La Trobe University: {"code":"23502","details":"Failing row contains (d4672cee-6aea-4b49-a606-a174aa46c800, La Trobe University, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:15.745983+00, 2025-05-27 03:45:15.745983+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.745983+00, null, La, Trobe University, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,051 - ERROR - Failed to create customer Emma Reid: {"code":"23502","details":"Failing row contains (5f802837-2d93-4132-a56d-4f87e07ae52b, Emma Reid, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.827515+00, 2025-05-27 03:45:15.827515+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.827515+00, null, Emma, Reid, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,119 - ERROR - Failed to create customer FabFun: {"code":"23502","details":"Failing row contains (5af85e24-f524-4515-9026-007512df23eb, FabFun, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.901778+00, 2025-05-27 03:45:15.901778+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.901778+00, null, FabFun, , null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,212 - ERROR - Failed to create customer Dangerous Goods: {"code":"23502","details":"Failing row contains (0d60347f-e93a-4d62-bcf5-f36f10ca7568, Dangerous Goods, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:15.983409+00, 2025-05-27 03:45:15.983409+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:15.983409+00, null, Dangerous, Goods, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,297 - ERROR - Failed to create customer Claire Willis: {"code":"23502","details":"Failing row contains (bc6d6fd3-5e20-4d10-8ac6-bfcb80e2680d, Claire Willis, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:16.073775+00, 2025-05-27 03:45:16.073775+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.073775+00, null, Claire, Willis, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,375 - ERROR - Failed to create customer Veni May: {"code":"23502","details":"Failing row contains (924aa16a-2d07-44c7-8662-98c89691da3a, Veni May, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:16.157155+00, 2025-05-27 03:45:16.157155+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.157155+00, null, Veni, May, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,446 - ERROR - Failed to create customer Dandy Tyler: {"code":"23502","details":"Failing row contains (528480ac-ef5b-4535-8a45-b0ff25c4de05, Dandy Tyler, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:16.229057+00, 2025-05-27 03:45:16.229057+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.229057+00, null, Dandy, Tyler, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,520 - ERROR - Failed to create customer Eat The Beat: {"code":"23502","details":"Failing row contains (41b882b7-892f-4562-b05b-7f39a94d1238, Eat The Beat, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:16.303293+00, 2025-05-27 03:45:16.303293+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.303293+00, null, Eat, The Beat, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,588 - ERROR - Failed to create customer Frosty Solana Collective: {"code":"23502","details":"Failing row contains (97a2672a-04b5-4c49-b198-e74b7a118368, Frosty Solana Collective, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:16.371617+00, 2025-05-27 03:45:16.371617+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.371617+00, null, Frosty, Solana Collective, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,666 - ERROR - Failed to create customer La trobe: {"code":"23502","details":"Failing row contains (c1371047-cbcf-46ee-b928-27d6f0b01ce5, La trobe, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:16.449553+00, 2025-05-27 03:45:16.449553+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.449553+00, null, La, trobe, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,757 - INFO - Created customer: <EMAIL> (ID: 10600950-85b1-4705-910b-4ae5b8479747)
2025-05-27 13:45:20,827 - ERROR - Failed to create customer Balloonaversal Entertainments Peter Patterson: {"code":"23502","details":"Failing row contains (5babe5f7-120d-484b-bc76-4705cfdace1a, Balloonaversal Entertainments Peter Patterson, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:16.608621+00, 2025-05-27 03:45:16.608621+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.608621+00, null, Balloonaversal, Entertainments Peter Patterson, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:20,919 - ERROR - Failed to create customer Kstar Group ATF: {"code":"23502","details":"Failing row contains (7fd5319f-68a1-4f31-933a-7f232c6eb59f, Kstar Group ATF, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: b..., f, 2025-05-27 03:45:16.697341+00, 2025-05-27 03:45:16.697341+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.697341+00, null, Kstar, Group ATF, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:21,021 - ERROR - Failed to create customer Alexander  Vivian-riding: {"code":"23502","details":"Failing row contains (e3d77495-439f-4e2a-8df4-bc546d64d663, Alexander  Vivian-riding, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:16.794816+00, 2025-05-27 03:45:16.794816+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.794816+00, null, Alexander, Vivian-riding, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:21,097 - ERROR - Failed to create customer Rhys Marsh: {"code":"23502","details":"Failing row contains (2532601e-f5fb-49c3-8ced-e570a5d25cef, Rhys Marsh, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:16.872739+00, 2025-05-27 03:45:16.872739+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.872739+00, null, Rhys, Marsh, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:21,175 - ERROR - Failed to create customer Riely Saville: {"code":"23502","details":"Failing row contains (9be28d1a-115c-4bf9-b189-ca7e5cc6a8ca, Riely Saville, null, null, null, null, null, null, Australia, Auto-created during Wix invoice migration on 2025-05-27. Type: i..., f, 2025-05-27 03:45:16.957591+00, 2025-05-27 03:45:16.957591+00, null, t, null, null, null, 2025-05-27, 0.00, null, 0, f, null, 50, 0.00, email, normal, null, null, 2025-05-27, null, active, null, null, bronze, 2025-05-27 03:45:16.957591+00, null, Riely, Saville, null, null, never_subscribed, never_subscribed, null, 0.00, 0, null, null, null, en, null, null, f).","hint":null,"message":"null value in column \"email\" of relation \"customers\" violates not-null constraint"}
2025-05-27 13:45:21,182 - INFO - Generated customer creation report
2025-05-27 13:45:21,184 - INFO - Missing customer creation completed successfully!
2025-05-27 13:46:06,111 - INFO - Starting missing customer creation process...
2025-05-27 13:46:06,113 - INFO - Loaded 58 manual review cases
2025-05-27 13:46:06,113 - INFO - Found 41 unique missing customers
2025-05-27 13:46:06,113 - INFO - Creating 41 missing customers...
2025-05-27 13:46:06,249 - INFO - Created customer: MYOB (ID: 6784a289-bb60-41c7-90ae-3d50a057c372)
2025-05-27 13:46:06,408 - INFO - Created customer: Rachel Coppel (ID: 082c1609-678b-43a6-bd94-7f6222b6bea1)
2025-05-27 13:46:06,505 - INFO - Created customer: Kstar Group ATF (ID: 0e456f13-6056-49d7-af8f-d4c5b9581871)
2025-05-27 13:46:06,596 - INFO - Created customer: Antonella Face Painting Melbourne (ID: e21edb81-2646-4e87-9c4f-de620ff82f60)
2025-05-27 13:46:06,717 - INFO - Created customer: Alexander  Vivian-riding (ID: b23fae84-f3ac-4659-bda9-9a4e82aac573)
2025-05-27 13:46:06,802 - INFO - Created customer: Riely Saville (ID: da65d655-d687-412a-aec3-32ada14a145e)
2025-05-27 13:46:06,883 - INFO - Created customer: Dandy Tyler (ID: cb6b42f0-3af3-46df-9824-ae75e2f3b353)
2025-05-27 13:46:06,993 - INFO - Created customer: Rhys Marsh (ID: 5ce90696-03f8-4a02-b8ef-5cc0874aab0d)
2025-05-27 13:46:07,106 - INFO - Created customer: Miriam (ID: 411ea928-4cb6-4365-b0c9-eb8af5d15bae)
2025-05-27 13:46:07,193 - INFO - Created customer: Milan Mili (La Trobe) (ID: 7b70290d-ae02-43ab-aa31-de5770442ee4)
2025-05-27 13:46:07,382 - INFO - Created customer: Nelson Alexander (ID: df064888-d180-4705-8294-6f4724e8fd9b)
2025-05-27 13:46:07,475 - INFO - Created customer: Claire Willis (ID: b2e471b5-e2df-474c-8d4a-4429b07d2c60)
2025-05-27 13:46:07,558 - INFO - Created customer: Clarissa PaintNSparkles (ID: 8ff7ce7b-857a-4ae3-86b8-5055ccc0c148)
2025-05-27 13:46:07,645 - INFO - Created customer: MAGIC MOMENTS ENTERTAINMENT PTY LTD. Priyanshu Tomar (ID: 1c591ba7-7d9c-4977-b1e9-a7d010d2c9d0)
2025-05-27 13:46:07,857 - INFO - Created customer: Chloe Selleck (ID: e7fc2d87-b8dd-4414-8d65-353d5b4668e2)
2025-05-27 13:46:07,932 - INFO - Created customer: Veni May (ID: da6c02c1-3da4-468c-9119-021181776921)
2025-05-27 13:46:08,011 - INFO - Created customer: Kenny Z (ID: 8299fda1-ea32-4362-8b76-37e492cb18d5)
2025-05-27 13:46:08,199 - INFO - Created customer: Rochelle (ID: d602f72a-b048-443e-8494-81725f52eb21)
2025-05-27 13:46:08,292 - INFO - Created customer: Patricia Bowlby (ID: d1fc4060-9bd9-435b-b142-d2f16baf87d3)
2025-05-27 13:46:08,387 - INFO - Created customer: TJ Pockets (ID: 0bc21d39-0da9-45fd-9c84-cc0f89a50ada)
2025-05-27 13:46:08,473 - ERROR - Failed to <NAME_EMAIL>: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 13:46:08,690 - INFO - Created customer: Ivanhoe East Primary School (ID: ef996d58-60de-45e5-a3c4-a06d6982d53f)
2025-05-27 13:46:08,778 - INFO - Created customer: Frosty Solana Collective (ID: d5852e38-2f3e-4c65-9ed7-47afe84c24c0)
2025-05-27 13:46:08,868 - INFO - Created customer: Balloonaversal Entertainments Peter Patterson (ID: 8a004a62-c24f-4c91-857d-8ff9aab4dd28)
2025-05-27 13:46:09,072 - INFO - Created customer: La trobe (ID: a28cfb9f-cda1-4ebf-91b9-06eff9fa6cfc)
2025-05-27 13:46:09,184 - INFO - Created customer: Luke Priday (ID: 63df655b-329c-4171-babb-b27883a6538e)
2025-05-27 13:46:09,259 - INFO - Created customer: Danni Patterson (ID: bb3653ba-ace4-4d3d-af40-98dfce2ca940)
2025-05-27 13:46:09,397 - INFO - Created customer: La Trobe University (ID: 6818df3a-020e-4bf6-bea4-5d8216d8b632)
2025-05-27 13:46:09,510 - INFO - Created customer: Jatinder Bhatti (ID: 6424116b-90ae-46be-b672-f2d8bb35d930)
2025-05-27 13:46:09,619 - INFO - Created customer: Dangerous Goods (ID: 8455deea-ec3c-41cf-9600-c74af80c4f45)
2025-05-27 13:46:09,705 - INFO - Created customer: Officeworks Accounts Payable (ID: ********-24cc-41ec-9855-db5306dca020)
2025-05-27 13:46:09,940 - INFO - Created customer: FabFun (ID: 1e63baf6-63cf-4243-87c3-7df2a9a18d02)
2025-05-27 13:46:10,028 - INFO - Created customer: Emma Reid (ID: 6519da68-177a-41a3-9597-19a3ab5e4ae5)
2025-05-27 13:46:10,113 - INFO - Created customer: YMCA / YES Youth Hub (ID: 5adde797-cb14-4676-b35c-1a1ac20a0545)
2025-05-27 13:46:10,323 - INFO - Created customer: Ally Maye (ID: ceab35c3-bd5d-4064-a0d7-7b99683ff0fb)
2025-05-27 13:46:10,424 - INFO - Created customer: Sub Club Melbourne Jonathan Tiatia (ID: 2697da3f-2f17-4e98-b77e-31661730774f)
2025-05-27 13:46:10,507 - INFO - Created customer: Eat The Beat (ID: e0ead11b-64bc-4116-8d10-ccad8cb94ff0)
2025-05-27 13:46:10,737 - INFO - Created customer: Casey Vance (ID: 35304dd3-412e-4dd4-83c5-5830c37b6daf)
2025-05-27 13:46:10,825 - INFO - Created customer: Abi (ID: e8f6e389-21b3-40e8-a57b-1e6ed4c25264)
2025-05-27 13:46:10,917 - INFO - Created customer: Electric Lady Land Matt Ambler (ID: 030b7931-9b58-4279-a5a4-1cb9aa637f21)
2025-05-27 13:46:11,157 - INFO - Created customer: Monbulk College Parents Club (ID: fcea4e31-a287-438e-898b-dcc040208665)
2025-05-27 13:46:11,158 - INFO - Generated customer creation report
2025-05-27 13:46:11,159 - INFO - Missing customer creation completed successfully!
