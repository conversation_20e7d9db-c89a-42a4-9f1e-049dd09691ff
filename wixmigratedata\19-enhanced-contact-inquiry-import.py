#!/usr/bin/env python3

"""
Enhanced Contact Inquiry Import Script for Ocean Soul Sparkles
Phase 2B: Contact Form Submissions Import

This script imports contact form submissions from the Wix migration data using
the same successful three-phase approach used for invoice import:
1. Exact matches with existing customers
2. Customer creation for new contacts
3. Manual review for low confidence matches

Data source: Contact+3.csv (134 contact form submissions)
"""

import pandas as pd
import requests
import uuid
import logging
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import os
from dotenv import load_dotenv
from fuzzywuzzy import fuzz

# Load environment variables
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced-contact-inquiry-import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedContactInquiryImporter:
    def __init__(self):
        self.supabase_url = os.getenv('NEXT_PUBLIC_SUPABASE_URL', '').strip()
        self.supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY', '').strip()

        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Missing Supabase environment variables")

        self.headers = {
            'apikey': self.supabase_key.strip(),
            'Authorization': f'Bearer {self.supabase_key.strip()}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }

        # Load existing customers for matching
        self.customers = self.load_customers()

        # Statistics tracking
        self.match_results = {
            'exact_matches': 0,
            'fuzzy_matches': 0,
            'manual_review': 0,
            'no_matches': 0
        }

    def load_customers(self) -> List[Dict]:
        """Load all customers from database for matching."""
        try:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/customers?select=id,name,email,phone",
                headers=self.headers
            )

            if response.status_code == 200:
                customers = response.json()
                logger.info(f"Loaded {len(customers)} customers for matching")
                return customers
            else:
                logger.error(f"Failed to load customers: {response.text}")
                return []

        except Exception as e:
            logger.error(f"Error loading customers: {str(e)}")
            return []

    def find_customer_match(self, contact_name: str, contact_email: str, contact_phone: str) -> Tuple[Optional[str], str, float]:
        """
        Find matching customer using enhanced fuzzy matching.
        Returns: (customer_id, match_type, confidence)
        """
        if not self.customers:
            return None, 'no_match', 0.0

        best_match = None
        best_score = 0.0
        match_type = 'no_match'

        # Clean inputs
        clean_name = self.clean_field(contact_name) or ''
        clean_email = self.clean_field(contact_email) or ''
        clean_phone = self.normalize_phone(contact_phone) or ''

        for customer in self.customers:
            # Email exact match (highest priority)
            if clean_email and customer.get('email'):
                if clean_email.lower() == customer['email'].lower():
                    return customer['id'], 'email_exact', 1.0

            # Phone exact match
            if clean_phone and customer.get('phone'):
                customer_phone = self.normalize_phone(customer['phone'])
                if clean_phone == customer_phone:
                    return customer['id'], 'phone_exact', 1.0

            # Name fuzzy matching
            if clean_name:
                # Try full name match
                customer_full_name = customer.get('name', '')

                if customer_full_name:
                    name_score = fuzz.ratio(clean_name.lower(), customer_full_name.lower()) / 100.0

                    # Check for known variations
                    if self.is_known_variation(clean_name, customer_full_name):
                        name_score = max(name_score, 0.95)

                    if name_score > best_score:
                        best_match = customer['id']
                        best_score = name_score

                        if name_score >= 0.95:
                            match_type = 'exact_match'
                        elif name_score >= 0.85:
                            match_type = 'fuzzy_high'
                        elif name_score >= 0.70:
                            match_type = 'fuzzy_medium'
                        else:
                            match_type = 'fuzzy_low'

        return best_match, match_type, best_score

    def is_known_variation(self, name1: str, name2: str) -> bool:
        """Check if names are known variations of each other."""
        # Common nickname mappings
        nickname_map = {
            'jess': 'jessica', 'jessie': 'jessica', 'jes': 'jessica',
            'kate': 'katherine', 'katie': 'katherine', 'kathy': 'katherine',
            'bec': 'rebecca', 'becky': 'rebecca',
            'ally': 'alison', 'ali': 'alison',
            'pat': 'patricia', 'patty': 'patricia',
            'cam': 'cameron', 'cammy': 'cameron'
        }

        name1_lower = name1.lower().strip()
        name2_lower = name2.lower().strip()

        # Check direct nickname mapping
        for nick, full in nickname_map.items():
            if (nick in name1_lower and full in name2_lower) or (nick in name2_lower and full in name1_lower):
                return True

        # Check if one name is contained in the other (for compound names)
        if len(name1_lower) > 3 and len(name2_lower) > 3:
            if name1_lower in name2_lower or name2_lower in name1_lower:
                return True

        return False

    def normalize_phone(self, phone: str) -> Optional[str]:
        """Normalize phone number to consistent format."""
        if not phone:
            return None

        # Remove all non-digit characters
        digits = re.sub(r'[^\d]', '', str(phone))

        # Handle Australian phone numbers
        if digits.startswith('61'):
            digits = digits[2:]  # Remove country code
        elif digits.startswith('0'):
            digits = digits[1:]  # Remove leading 0

        # Should be 9 digits for Australian mobile/landline
        if len(digits) == 9:
            return f"+61{digits}"

        return None

    def clean_field(self, value) -> Optional[str]:
        """Clean text field."""
        if pd.isna(value) or value == '' or str(value) == 'nan':
            return None

        cleaned = str(value).strip()
        return cleaned if cleaned else None

    def parse_datetime(self, datetime_str) -> Optional[str]:
        """Parse datetime string to ISO format."""
        if pd.isna(datetime_str) or datetime_str == '':
            return None

        try:
            # Handle ISO format with Z suffix
            if isinstance(datetime_str, str) and datetime_str.endswith('Z'):
                dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
                return dt.isoformat()

            # Try parsing as datetime object
            if hasattr(datetime_str, 'isoformat'):
                return datetime_str.isoformat()

            return str(datetime_str)
        except Exception as e:
            logger.warning(f"Could not parse datetime: '{datetime_str}': {str(e)}")
            return None

    def prepare_contact_inquiry_record(self, row, customer_id: Optional[str], contact_name: str,
                                     match_type: str, confidence: float) -> Dict:
        """Prepare contact inquiry record for import."""

        record = {
            'customer_id': customer_id,
            'subject': self.clean_field(row.get('Subject')),
            'message': self.clean_field(row.get('Message')),
            'submitted_at': self.parse_datetime(row.get('Submission Time')),
            'status': 'new'  # Default status for imported inquiries
        }

        # Remove None values except for customer_id (which can be None for manual review)
        return {k: v for k, v in record.items() if v is not None or k == 'customer_id'}

    def create_customer_from_inquiry(self, row) -> Optional[str]:
        """Create a new customer from contact inquiry data."""
        try:
            contact_name = self.clean_field(row.get('Name'))
            contact_email = self.clean_field(row.get('Email'))
            contact_phone = self.clean_field(row.get('Phone'))

            if not contact_name:
                return None

            # Parse name into first/last
            if '@' in contact_name:
                # If name is actually an email, use email part as first name
                first_name = contact_name.split('@')[0]
                last_name = ''
                if not contact_email:
                    contact_email = contact_name
            else:
                name_parts = contact_name.strip().split()
                if len(name_parts) >= 2:
                    first_name = name_parts[0]
                    last_name = ' '.join(name_parts[1:])
                else:
                    first_name = contact_name
                    last_name = ''

            # Generate placeholder email if none provided
            if not contact_email:
                safe_name = re.sub(r'[^a-zA-Z0-9]', '', contact_name.lower())[:20]
                contact_email = f"{safe_name}@inquiry.oceansoulsparkles.local"

            customer_record = {
                'name': contact_name,
                'email': contact_email,
                'phone': self.normalize_phone(contact_phone),
                'notes': f'Auto-created from contact inquiry on {datetime.now().strftime("%Y-%m-%d")}'
            }

            # Remove None values
            customer_record = {k: v for k, v in customer_record.items() if v is not None}

            response = requests.post(
                f"{self.supabase_url}/rest/v1/customers",
                headers=self.headers,
                json=customer_record
            )

            if response.status_code in [200, 201]:
                created_customer = response.json()
                if isinstance(created_customer, list) and len(created_customer) > 0:
                    customer_id = created_customer[0]['id']
                else:
                    customer_id = created_customer['id']

                logger.info(f"Created customer: {contact_name} (ID: {customer_id})")
                return customer_id
            else:
                logger.error(f"Failed to create customer {contact_name}: {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error creating customer from inquiry: {str(e)}")
            return None

    def import_contact_inquiry(self, inquiry_record: Dict) -> bool:
        """Import a single contact inquiry."""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/contact_inquiries",
                headers=self.headers,
                json=inquiry_record
            )

            if response.status_code in [200, 201]:
                return True
            else:
                logger.error(f"Failed to import inquiry: {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error importing inquiry: {str(e)}")
            return False

    def process_contact_inquiries(self) -> bool:
        """Main function to process all contact inquiries."""
        logger.info("Starting enhanced contact inquiry import process...")

        # Load contact form data
        try:
            # Use proper CSV parsing to handle multiline fields
            df = pd.read_csv('../Contact+3.csv', quotechar='"', escapechar='\\', skipinitialspace=True)
            logger.info(f"Loaded {len(df)} contact inquiries from CSV")

            # Debug: Check if we have all the data
            if len(df) < 100:
                logger.warning(f"Expected ~134 inquiries but only loaded {len(df)}. Checking file...")
                # Try alternative parsing
                try:
                    df_alt = pd.read_csv('../Contact+3.csv', quoting=1, doublequote=True)
                    if len(df_alt) > len(df):
                        df = df_alt
                        logger.info(f"Alternative parsing loaded {len(df)} inquiries")
                except Exception as e2:
                    logger.warning(f"Alternative parsing failed: {str(e2)}")
        except Exception as e:
            logger.error(f"Failed to load contact inquiry data: {str(e)}")
            return False

        # Clean column names
        df.columns = [col.replace('"', '').strip() for col in df.columns]

        # Statistics tracking
        imported_count = 0
        failed_count = 0
        manual_review_cases = []

        for index, row in df.iterrows():
            try:
                contact_name = self.clean_field(row.get('Name'))
                contact_email = self.clean_field(row.get('Email'))
                contact_phone = self.clean_field(row.get('Phone'))

                if not contact_name:
                    logger.warning(f"Skipping row {index}: No contact name")
                    continue

                logger.info(f"Processing inquiry from: '{contact_name}'")

                # Find customer match
                customer_id, match_type, confidence = self.find_customer_match(
                    contact_name, contact_email, contact_phone
                )

                # Handle different match types
                if match_type in ['email_exact', 'phone_exact', 'exact_match']:
                    # Direct import for high confidence matches
                    self.match_results['exact_matches'] += 1

                elif match_type in ['fuzzy_high', 'fuzzy_medium'] and confidence >= 0.80:
                    # Import fuzzy matches with high confidence
                    self.match_results['fuzzy_matches'] += 1

                elif match_type in ['fuzzy_medium', 'fuzzy_low'] and confidence >= 0.70:
                    # Add to manual review for medium confidence
                    self.match_results['manual_review'] += 1
                    inquiry_data = self.prepare_contact_inquiry_record(
                        row, customer_id, contact_name, match_type, confidence
                    )
                    manual_review_cases.append({
                        'contact_name': contact_name,
                        'contact_email': contact_email,
                        'contact_phone': contact_phone,
                        'suggested_customer_id': customer_id,
                        'match_type': match_type,
                        'confidence': confidence,
                        'inquiry_data': inquiry_data
                    })
                    continue

                else:
                    # No match - create new customer
                    self.match_results['no_matches'] += 1
                    customer_id = self.create_customer_from_inquiry(row)
                    if not customer_id:
                        logger.error(f"Failed to create customer for inquiry from {contact_name}")
                        failed_count += 1
                        continue
                    match_type = 'new_customer'
                    confidence = 1.0

                # Prepare and import inquiry
                inquiry_record = self.prepare_contact_inquiry_record(
                    row, customer_id, contact_name, match_type, confidence
                )

                if self.import_contact_inquiry(inquiry_record):
                    imported_count += 1
                    logger.info(f"Imported inquiry from {contact_name} (match: {match_type})")
                else:
                    failed_count += 1

            except Exception as e:
                logger.error(f"Error processing row {index}: {str(e)}")
                failed_count += 1

        # Save manual review cases
        if manual_review_cases:
            with open('manual-review-contact-inquiries.json', 'w') as f:
                json.dump(manual_review_cases, f, indent=2, default=str)
            logger.info(f"Saved {len(manual_review_cases)} cases for manual review")

        # Generate report
        self.generate_report(imported_count, failed_count, len(manual_review_cases))

        return True

    def generate_report(self, imported_count: int, failed_count: int, manual_review_count: int):
        """Generate comprehensive import report."""
        total_processed = imported_count + failed_count + manual_review_count
        success_rate = (imported_count / total_processed * 100) if total_processed > 0 else 0

        report = f"""
# Enhanced Contact Inquiry Import Report
**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Status:** COMPLETED

## Summary
- **Total inquiries processed:** {total_processed}
- **Successfully imported:** {imported_count}
- **Failed to import:** {failed_count}
- **Manual review required:** {manual_review_count}
- **Success rate:** {success_rate:.1f}%

## Matching Results
- **Exact matches:** {self.match_results['exact_matches']} (email/phone/name exact)
- **Fuzzy matches:** {self.match_results['fuzzy_matches']} (high confidence)
- **Manual review:** {self.match_results['manual_review']} (medium confidence)
- **New customers created:** {self.match_results['no_matches']}

## Import Statistics
- **Direct imports:** {imported_count - self.match_results['no_matches']} (existing customers)
- **New customer inquiries:** {self.match_results['no_matches']} (created customers)
- **Failed imports:** {failed_count}

## Next Steps
1. Review manual review cases in 'manual-review-contact-inquiries.json'
2. Import remaining inquiries with manual approval
3. Validate inquiry-customer relationships
4. Proceed with email campaigns import
5. Complete Phase 2B data migration

## Data Quality Notes
- All inquiries maintain original submission timestamps
- Customer relationships preserved where possible
- New customers created for unknown contacts
- Comprehensive audit trail established
"""

        with open('enhanced-contact-inquiry-import-report.md', 'w') as f:
            f.write(report)

        logger.info("Generated contact inquiry import report")
        print(report)

def main():
    """Main execution function."""
    importer = EnhancedContactInquiryImporter()

    if not importer.process_contact_inquiries():
        logger.error("Contact inquiry import process failed.")
        return False

    logger.info("Contact inquiry import completed successfully!")
    return True

if __name__ == "__main__":
    main()
