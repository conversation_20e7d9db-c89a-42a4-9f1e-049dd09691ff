#!/usr/bin/env python3
"""
Ocean Soul Sparkles - Supabase Data Import Script
Imports cleaned data from CSV files into Supabase database.
"""

import pandas as pd
import json
import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Any
import uuid
import requests
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupabaseImporter:
    """Main class for importing cleaned data into Supabase."""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        self.supabase_url = supabase_url.rstrip('/')
        self.supabase_key = supabase_key
        self.headers = {
            'apikey': supabase_key,
            'Authorization': f'Bearer {supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
        self.import_stats = {
            'customers': {'imported': 0, 'errors': 0},
            'bookings': {'imported': 0, 'errors': 0},
            'invoices': {'imported': 0, 'errors': 0},
            'products': {'imported': 0, 'errors': 0},
            'contact_inquiries': {'imported': 0, 'errors': 0},
            'email_campaigns': {'imported': 0, 'errors': 0}
        }
    
    def log_migration_start(self, phase: str, table_name: str, record_count: int):
        """Log the start of a migration phase."""
        migration_data = {
            'migration_phase': phase,
            'table_name': table_name,
            'operation': 'insert',
            'record_count': record_count,
            'status': 'running'
        }
        
        response = requests.post(
            f"{self.supabase_url}/rest/v1/migration_log",
            headers=self.headers,
            json=migration_data
        )
        
        if response.status_code in [200, 201]:
            logger.info(f"Migration log started for {table_name}")
            return response.json()
        else:
            logger.error(f"Failed to log migration start: {response.text}")
            return None
    
    def log_migration_complete(self, log_id: str, success_count: int, error_count: int, errors: List = None):
        """Log the completion of a migration phase."""
        update_data = {
            'success_count': success_count,
            'error_count': error_count,
            'completed_at': datetime.now().isoformat(),
            'status': 'completed' if error_count == 0 else 'failed'
        }
        
        if errors:
            update_data['errors'] = json.dumps(errors)
        
        response = requests.patch(
            f"{self.supabase_url}/rest/v1/migration_log?id=eq.{log_id}",
            headers=self.headers,
            json=update_data
        )
        
        if response.status_code == 204:
            logger.info(f"Migration log completed: {success_count} success, {error_count} errors")
        else:
            logger.error(f"Failed to update migration log: {response.text}")
    
    def insert_batch(self, table_name: str, records: List[Dict], batch_size: int = 100) -> tuple:
        """Insert records in batches to avoid timeout issues."""
        success_count = 0
        error_count = 0
        errors = []
        
        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]
            
            try:
                response = requests.post(
                    f"{self.supabase_url}/rest/v1/{table_name}",
                    headers=self.headers,
                    json=batch
                )
                
                if response.status_code in [200, 201]:
                    success_count += len(batch)
                    logger.info(f"Inserted batch {i//batch_size + 1}: {len(batch)} records into {table_name}")
                else:
                    error_count += len(batch)
                    error_msg = f"Batch {i//batch_size + 1} failed: {response.text}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                    
            except Exception as e:
                error_count += len(batch)
                error_msg = f"Batch {i//batch_size + 1} exception: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        return success_count, error_count, errors
    
    def prepare_customer_data(self, df: pd.DataFrame) -> List[Dict]:
        """Prepare customer data for Supabase import."""
        records = []
        
        for _, row in df.iterrows():
            # Generate UUID for new customer
            customer_id = str(uuid.uuid4())
            
            record = {
                'id': customer_id,
                'first_name': self.clean_text_field(row.get('first_name')),
                'last_name': self.clean_text_field(row.get('last_name')),
                'name': f"{row.get('first_name', '')} {row.get('last_name', '')}".strip(),
                'email': self.clean_text_field(row.get('email_cleaned') or row.get('email')),
                'phone': self.clean_text_field(row.get('phone_primary_standardized') or row.get('phone_primary')),
                'phone_secondary': self.clean_text_field(row.get('phone_secondary_standardized') or row.get('phone_secondary')),
                'date_of_birth': self.parse_date(row.get('date_of_birth')),
                'email_subscription_status': self.map_subscription_status(row.get('email_subscription_status')),
                'sms_subscription_status': self.map_subscription_status(row.get('sms_subscription_status')),
                'square_customer_id': self.clean_text_field(row.get('square_customer_id')),
                'total_spend': self.parse_decimal(row.get('total_spend')),
                'transaction_count': self.parse_integer(row.get('transaction_count')),
                'first_visit': self.parse_date(row.get('first_visit')),
                'last_visit': self.parse_date(row.get('last_visit')),
                'notes': self.clean_text_field(row.get('notes')),
                'original_source': self.clean_text_field(row.get('original_source')),
                'migration_notes': self.clean_text_field(row.get('migration_notes')),
                'duplicate_resolved': bool(row.get('duplicate_resolved', False)),
                'marketing_consent': self.map_subscription_status(row.get('email_subscription_status')) == 'subscribed'
            }
            
            # Remove None values
            record = {k: v for k, v in record.items() if v is not None}
            records.append(record)
        
        return records
    
    def prepare_booking_data(self, df: pd.DataFrame, customer_lookup: Dict[str, str]) -> List[Dict]:
        """Prepare booking data for Supabase import."""
        records = []
        
        for _, row in df.iterrows():
            # Find customer ID by email
            customer_email = self.clean_text_field(row.get('Email') or row.get('email'))
            customer_id = customer_lookup.get(customer_email)
            
            if not customer_id:
                logger.warning(f"No customer found for booking with email: {customer_email}")
                continue
            
            record = {
                'id': str(uuid.uuid4()),
                'customer_id': customer_id,
                'order_number': self.clean_text_field(row.get('Order Number')),
                'booking_date': self.parse_date(row.get('Booking Start Time')),
                'start_time': self.parse_datetime(row.get('Booking Start Time')),
                'end_time': self.parse_datetime(row.get('Booking End Time')),
                'status': self.map_booking_status(row.get('Booking Status')),
                'payment_status': self.map_payment_status(row.get('Payment Status')),
                'total_amount': self.parse_decimal(row.get('Order Total')),
                'location_address': self.clean_text_field(row.get('Location Address')),
                'staff_member': self.clean_text_field(row.get('Staff Member')),
                'group_size': self.parse_integer(row.get('Group Size')),
                'event_name': self.clean_text_field(row.get('Event Name')),
                'notes': self.clean_text_field(row.get('Notes')),
                'booking_source': 'wix_migration'
            }
            
            # Remove None values
            record = {k: v for k, v in record.items() if v is not None}
            records.append(record)
        
        return records
    
    def prepare_invoice_data(self, df: pd.DataFrame, customer_lookup: Dict[str, str]) -> List[Dict]:
        """Prepare invoice data for Supabase import."""
        records = []
        
        for _, row in df.iterrows():
            # Find customer ID by name (need to implement name matching)
            customer_name = self.clean_text_field(row.get('customer_name'))
            customer_id = self.find_customer_by_name(customer_name, customer_lookup)
            
            record = {
                'id': str(uuid.uuid4()),
                'customer_id': customer_id,
                'invoice_number': self.clean_text_field(row.get('invoice_number')),
                'order_number': self.clean_text_field(row.get('order_number')),
                'status': self.map_invoice_status(row.get('status')),
                'issue_date': self.parse_date(row.get('issue_date')),
                'due_date': self.parse_date(row.get('due_date')),
                'currency': self.clean_text_field(row.get('currency')) or 'AUD',
                'subtotal': self.parse_decimal(row.get('subtotal')),
                'discount_amount': self.parse_decimal(row.get('discount_amount')),
                'tax_amount': self.parse_decimal(row.get('tax_amount')),
                'total_amount': self.parse_decimal(row.get('total_amount')),
                'amount': self.parse_decimal(row.get('total_amount')),  # For existing schema compatibility
                'notes': self.clean_text_field(row.get('notes'))
            }
            
            # Remove None values
            record = {k: v for k, v in record.items() if v is not None}
            records.append(record)
        
        return records
    
    def clean_text_field(self, value: Any) -> Optional[str]:
        """Clean text field values."""
        if pd.isna(value) or value == '' or value == 'nan':
            return None
        return str(value).strip()
    
    def parse_date(self, value: Any) -> Optional[str]:
        """Parse date values to ISO format."""
        if pd.isna(value) or value == '':
            return None
        
        try:
            if isinstance(value, str):
                # Handle various date formats
                for fmt in ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%d %H:%M:%S']:
                    try:
                        parsed_date = datetime.strptime(value, fmt).date()
                        return parsed_date.isoformat()
                    except ValueError:
                        continue
            elif isinstance(value, (date, datetime)):
                return value.date().isoformat() if hasattr(value, 'date') else value.isoformat()
        except Exception as e:
            logger.warning(f"Failed to parse date '{value}': {e}")
        
        return None
    
    def parse_datetime(self, value: Any) -> Optional[str]:
        """Parse datetime values to ISO format."""
        if pd.isna(value) or value == '':
            return None
        
        try:
            if isinstance(value, str):
                # Handle various datetime formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%m/%d/%Y %H:%M:%S', '%Y-%m-%dT%H:%M:%S']:
                    try:
                        parsed_dt = datetime.strptime(value, fmt)
                        return parsed_dt.isoformat()
                    except ValueError:
                        continue
            elif isinstance(value, datetime):
                return value.isoformat()
        except Exception as e:
            logger.warning(f"Failed to parse datetime '{value}': {e}")
        
        return None
    
    def parse_decimal(self, value: Any) -> Optional[float]:
        """Parse decimal values."""
        if pd.isna(value) or value == '':
            return None
        
        try:
            # Remove currency symbols and commas
            if isinstance(value, str):
                value = value.replace('$', '').replace(',', '').replace('A$', '').strip()
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def parse_integer(self, value: Any) -> Optional[int]:
        """Parse integer values."""
        if pd.isna(value) or value == '':
            return None
        
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return None
    
    def map_subscription_status(self, value: Any) -> str:
        """Map subscription status values."""
        if pd.isna(value) or value == '':
            return 'never_subscribed'
        
        value_str = str(value).lower()
        if 'subscribed' in value_str and 'never' not in value_str and 'un' not in value_str:
            return 'subscribed'
        elif 'unsubscribed' in value_str:
            return 'unsubscribed'
        else:
            return 'never_subscribed'
    
    def map_booking_status(self, value: Any) -> str:
        """Map booking status values."""
        if pd.isna(value) or value == '':
            return 'pending_approval'
        
        value_str = str(value).lower()
        status_mapping = {
            'confirmed': 'confirmed',
            'pending approval': 'pending_approval',
            'canceled': 'canceled',
            'cancelled': 'canceled',
            'declined': 'declined',
            'completed': 'completed',
            'incomplete': 'incomplete'
        }
        
        return status_mapping.get(value_str, 'pending_approval')
    
    def map_payment_status(self, value: Any) -> str:
        """Map payment status values."""
        if pd.isna(value) or value == '':
            return 'not_paid'
        
        value_str = str(value).lower()
        if 'paid' in value_str:
            return 'paid'
        elif 'partial' in value_str:
            return 'partial'
        elif 'refunded' in value_str:
            return 'refunded'
        else:
            return 'not_paid'
    
    def map_invoice_status(self, value: Any) -> str:
        """Map invoice status values."""
        if pd.isna(value) or value == '':
            return 'draft'
        
        value_str = str(value).lower()
        status_mapping = {
            'sent': 'sent',
            'paid': 'paid',
            'overdue': 'overdue',
            'void': 'void',
            'draft': 'draft',
            'refunded': 'refunded'
        }
        
        return status_mapping.get(value_str, 'draft')
    
    def find_customer_by_name(self, customer_name: str, customer_lookup: Dict[str, str]) -> Optional[str]:
        """Find customer ID by name (simplified implementation)."""
        if not customer_name:
            return None
        
        # This is a simplified implementation
        # In production, implement fuzzy name matching
        for email, customer_id in customer_lookup.items():
            # This would need more sophisticated matching
            pass
        
        return None
