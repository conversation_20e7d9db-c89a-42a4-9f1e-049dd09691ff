-- Ocean Soul Sparkles - Staging Database Setup
-- This script creates the staging database schema for data migration testing

-- Create database
CREATE DATABASE IF NOT EXISTS ocean_soul_sparkles_staging;
USE ocean_soul_sparkles_staging;

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- Customers table
CREATE TABLE customers (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    first_name VARCHAR(100),
    last_name VA<PERSON>HA<PERSON>(100),
    email VARCHAR(255) UNIQUE,
    phone_primary VARCHAR(20),
    phone_secondary VARCHAR(20),
    date_of_birth DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    email_subscription_status ENUM('subscribed', 'unsubscribed', 'never_subscribed') DEFAULT 'never_subscribed',
    sms_subscription_status ENUM('subscribed', 'unsubscribed', 'never_subscribed') DEFAULT 'never_subscribed',
    square_customer_id VARCHAR(100),
    total_spend DECIMAL(10,2) DEFAULT 0.00,
    transaction_count INTEGER DEFAULT 0,
    first_visit DATE,
    last_visit DATE,
    notes TEXT,
    source VARCHAR(100),
    language VARCHAR(10) DEFAULT 'en',
    
    -- Migration tracking fields
    original_source ENUM('google_contacts', 'regular_contacts', 'contact_form') NOT NULL,
    migration_notes TEXT,
    duplicate_resolved BOOLEAN DEFAULT FALSE,
    
    INDEX idx_email (email),
    INDEX idx_phone_primary (phone_primary),
    INDEX idx_square_customer_id (square_customer_id),
    INDEX idx_created_at (created_at)
);

-- Customer addresses table
CREATE TABLE customer_addresses (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    customer_id VARCHAR(36) NOT NULL,
    address_type ENUM('billing', 'shipping', 'work', 'home') DEFAULT 'home',
    street_line_1 VARCHAR(255),
    street_line_2 VARCHAR(255),
    city VARCHAR(100),
    state_region VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Australia',
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id),
    INDEX idx_address_type (address_type)
);

-- Companies table
CREATE TABLE companies (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    abn VARCHAR(20),
    industry VARCHAR(100),
    website VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_abn (abn)
);

-- Customer companies relationship table
CREATE TABLE customer_companies (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    customer_id VARCHAR(36) NOT NULL,
    company_id VARCHAR(36) NOT NULL,
    position VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id),
    INDEX idx_company_id (company_id)
);

-- Services table
CREATE TABLE services (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('face_painting', 'body_art', 'hair_braiding', 'glitter_bar', 'uv_painting', 'airbrush') NOT NULL,
    base_price DECIMAL(10,2),
    duration_minutes INTEGER,
    max_participants INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_is_active (is_active)
);

-- Service pricing table
CREATE TABLE service_pricing (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    service_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration_hours DECIMAL(3,1),
    max_participants INTEGER,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    INDEX idx_service_id (service_id),
    INDEX idx_is_active (is_active)
);

-- Bookings table
CREATE TABLE bookings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    customer_id VARCHAR(36) NOT NULL,
    order_number VARCHAR(50) UNIQUE,
    booking_date DATE NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status ENUM('confirmed', 'pending_approval', 'canceled', 'declined', 'completed', 'incomplete') DEFAULT 'pending_approval',
    total_amount DECIMAL(10,2),
    payment_status ENUM('paid', 'not_paid', 'partial', 'refunded') DEFAULT 'not_paid',
    location_address TEXT,
    special_instructions TEXT,
    staff_member VARCHAR(100),
    group_size INTEGER,
    event_name VARCHAR(255),
    event_type VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Migration tracking
    original_booking_id VARCHAR(50),
    data_quality_flags JSON,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_status (status),
    INDEX idx_order_number (order_number)
);

-- Contact inquiries table
CREATE TABLE contact_inquiries (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    customer_id VARCHAR(36),
    subject VARCHAR(255),
    message TEXT,
    inquiry_type ENUM('booking_request', 'general_inquiry', 'complaint', 'compliment') DEFAULT 'general_inquiry',
    status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
    submitted_at TIMESTAMP NOT NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Migration tracking
    original_submission_id VARCHAR(50),
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    INDEX idx_customer_id (customer_id),
    INDEX idx_submitted_at (submitted_at),
    INDEX idx_status (status)
);

-- Products table
CREATE TABLE products (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    handle_id VARCHAR(100),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    sku VARCHAR(100),
    price DECIMAL(10,2),
    cost DECIMAL(10,2),
    weight DECIMAL(5,2),
    inventory_quantity INTEGER DEFAULT 0,
    is_visible BOOLEAN DEFAULT TRUE,
    ribbon VARCHAR(50),
    collection VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_handle_id (handle_id),
    INDEX idx_sku (sku),
    INDEX idx_is_visible (is_visible)
);

-- Product images table
CREATE TABLE product_images (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    product_id VARCHAR(36) NOT NULL,
    image_url VARCHAR(500),
    alt_text VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_sort_order (sort_order)
);

-- Invoices table
CREATE TABLE invoices (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    customer_id VARCHAR(36),
    booking_id VARCHAR(36),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    order_number VARCHAR(50),
    status ENUM('draft', 'sent', 'paid', 'overdue', 'void', 'refunded') DEFAULT 'draft',
    issue_date DATE NOT NULL,
    due_date DATE,
    currency VARCHAR(3) DEFAULT 'AUD',
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE SET NULL,
    INDEX idx_customer_id (customer_id),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_status (status),
    INDEX idx_issue_date (issue_date)
);

-- Email campaigns table
CREATE TABLE email_campaigns (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    customer_id VARCHAR(36),
    email VARCHAR(255) NOT NULL,
    campaign_name VARCHAR(255),
    sent_at TIMESTAMP NOT NULL,
    status ENUM('sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained') DEFAULT 'sent',
    bounce_type ENUM('hard_bounce', 'soft_bounce'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    INDEX idx_customer_id (customer_id),
    INDEX idx_email (email),
    INDEX idx_sent_at (sent_at),
    INDEX idx_status (status)
);

-- ============================================================================
-- MIGRATION TRACKING TABLES
-- ============================================================================

-- Migration log table
CREATE TABLE migration_log (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    migration_phase ENUM('preparation', 'customers', 'bookings', 'products', 'invoices', 'validation') NOT NULL,
    table_name VARCHAR(100),
    operation ENUM('insert', 'update', 'delete', 'validate') NOT NULL,
    record_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    errors JSON,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    status ENUM('running', 'completed', 'failed') DEFAULT 'running',
    
    INDEX idx_migration_phase (migration_phase),
    INDEX idx_started_at (started_at),
    INDEX idx_status (status)
);

-- Data quality issues table
CREATE TABLE data_quality_issues (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(36),
    issue_type ENUM('duplicate', 'invalid_email', 'invalid_phone', 'missing_data', 'format_error') NOT NULL,
    issue_description TEXT,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    resolved BOOLEAN DEFAULT FALSE,
    resolution_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    
    INDEX idx_table_name (table_name),
    INDEX idx_issue_type (issue_type),
    INDEX idx_severity (severity),
    INDEX idx_resolved (resolved)
);

-- ============================================================================
-- VALIDATION VIEWS
-- ============================================================================

-- Customer data quality view
CREATE VIEW customer_data_quality AS
SELECT 
    c.id,
    c.first_name,
    c.last_name,
    c.email,
    c.phone_primary,
    CASE 
        WHEN c.email IS NULL OR c.email = '' THEN 'missing_email'
        WHEN c.email NOT REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN 'invalid_email'
        ELSE 'valid_email'
    END as email_status,
    CASE 
        WHEN c.phone_primary IS NULL OR c.phone_primary = '' THEN 'missing_phone'
        WHEN c.phone_primary NOT REGEXP '^\+[1-9][0-9]{8,14}$' THEN 'invalid_phone'
        ELSE 'valid_phone'
    END as phone_status,
    CASE 
        WHEN c.first_name IS NULL OR c.first_name = '' THEN 'missing_name'
        ELSE 'valid_name'
    END as name_status,
    c.duplicate_resolved,
    c.original_source
FROM customers c;

-- Revenue summary view (paid invoices only)
CREATE VIEW revenue_summary AS
SELECT 
    DATE_FORMAT(i.issue_date, '%Y-%m') as month,
    COUNT(*) as invoice_count,
    SUM(i.total_amount) as total_revenue,
    AVG(i.total_amount) as average_invoice
FROM invoices i
WHERE i.status = 'paid'
GROUP BY DATE_FORMAT(i.issue_date, '%Y-%m')
ORDER BY month;

-- Booking completion rate view
CREATE VIEW booking_metrics AS
SELECT 
    DATE_FORMAT(b.booking_date, '%Y-%m') as month,
    COUNT(*) as total_bookings,
    SUM(CASE WHEN b.status = 'completed' THEN 1 ELSE 0 END) as completed_bookings,
    SUM(CASE WHEN b.status = 'canceled' THEN 1 ELSE 0 END) as canceled_bookings,
    ROUND(SUM(CASE WHEN b.status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as completion_rate
FROM bookings b
GROUP BY DATE_FORMAT(b.booking_date, '%Y-%m')
ORDER BY month;

-- ============================================================================
-- STORED PROCEDURES FOR VALIDATION
-- ============================================================================

DELIMITER //

-- Procedure to validate customer data
CREATE PROCEDURE ValidateCustomerData()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE customer_id VARCHAR(36);
    DECLARE customer_email VARCHAR(255);
    DECLARE customer_phone VARCHAR(20);
    
    DECLARE customer_cursor CURSOR FOR 
        SELECT id, email, phone_primary FROM customers;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Clear previous validation results
    DELETE FROM data_quality_issues WHERE table_name = 'customers';
    
    OPEN customer_cursor;
    
    customer_loop: LOOP
        FETCH customer_cursor INTO customer_id, customer_email, customer_phone;
        
        IF done THEN
            LEAVE customer_loop;
        END IF;
        
        -- Validate email
        IF customer_email IS NULL OR customer_email = '' THEN
            INSERT INTO data_quality_issues (table_name, record_id, issue_type, issue_description, severity)
            VALUES ('customers', customer_id, 'missing_data', 'Missing email address', 'high');
        ELSEIF customer_email NOT REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
            INSERT INTO data_quality_issues (table_name, record_id, issue_type, issue_description, severity)
            VALUES ('customers', customer_id, 'invalid_email', CONCAT('Invalid email format: ', customer_email), 'medium');
        END IF;
        
        -- Validate phone
        IF customer_phone IS NOT NULL AND customer_phone != '' THEN
            IF customer_phone NOT REGEXP '^\+[1-9][0-9]{8,14}$' THEN
                INSERT INTO data_quality_issues (table_name, record_id, issue_type, issue_description, severity)
                VALUES ('customers', customer_id, 'invalid_phone', CONCAT('Invalid phone format: ', customer_phone), 'medium');
            END IF;
        END IF;
        
    END LOOP;
    
    CLOSE customer_cursor;
    
    -- Summary
    SELECT 
        issue_type,
        severity,
        COUNT(*) as issue_count
    FROM data_quality_issues 
    WHERE table_name = 'customers'
    GROUP BY issue_type, severity
    ORDER BY severity DESC, issue_count DESC;
    
END //

-- Procedure to check for duplicate customers
CREATE PROCEDURE CheckDuplicateCustomers()
BEGIN
    -- Email duplicates
    INSERT INTO data_quality_issues (table_name, record_id, issue_type, issue_description, severity)
    SELECT 
        'customers',
        c1.id,
        'duplicate',
        CONCAT('Duplicate email with customer ID: ', c2.id),
        'critical'
    FROM customers c1
    JOIN customers c2 ON c1.email = c2.email AND c1.id != c2.id
    WHERE c1.email IS NOT NULL AND c1.email != '';
    
    -- Phone duplicates
    INSERT INTO data_quality_issues (table_name, record_id, issue_type, issue_description, severity)
    SELECT 
        'customers',
        c1.id,
        'duplicate',
        CONCAT('Duplicate phone with customer ID: ', c2.id),
        'high'
    FROM customers c1
    JOIN customers c2 ON c1.phone_primary = c2.phone_primary AND c1.id != c2.id
    WHERE c1.phone_primary IS NOT NULL AND c1.phone_primary != '';
    
END //

DELIMITER ;

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Insert initial migration log entry
INSERT INTO migration_log (migration_phase, operation, record_count, status)
VALUES ('preparation', 'validate', 0, 'completed');

-- Create indexes for performance
CREATE INDEX idx_customers_email_phone ON customers(email, phone_primary);
CREATE INDEX idx_bookings_date_status ON bookings(booking_date, status);
CREATE INDEX idx_invoices_date_status ON invoices(issue_date, status);

-- Set up initial validation
CALL ValidateCustomerData();

-- Display setup completion message
SELECT 'Ocean Soul Sparkles staging database setup completed successfully!' as message;
