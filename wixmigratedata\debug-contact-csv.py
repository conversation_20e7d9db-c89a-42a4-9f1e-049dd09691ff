#!/usr/bin/env python3

"""
Debug script to check Contact+3.csv parsing issues
"""

import pandas as pd
import csv

def test_csv_parsing():
    """Test different CSV parsing methods."""
    
    print("=== Testing CSV Parsing Methods ===")
    
    # Method 1: Default pandas
    try:
        df1 = pd.read_csv('../Contact+3.csv')
        print(f"Method 1 (default pandas): {len(df1)} rows")
    except Exception as e:
        print(f"Method 1 failed: {str(e)}")
    
    # Method 2: With quote handling
    try:
        df2 = pd.read_csv('../Contact+3.csv', quotechar='"', escapechar='\\')
        print(f"Method 2 (quote handling): {len(df2)} rows")
    except Exception as e:
        print(f"Method 2 failed: {str(e)}")
    
    # Method 3: With different quoting
    try:
        df3 = pd.read_csv('../Contact+3.csv', quoting=csv.QUOTE_ALL)
        print(f"Method 3 (quote all): {len(df3)} rows")
    except Exception as e:
        print(f"Method 3 failed: {str(e)}")
    
    # Method 4: Manual CSV reader
    try:
        with open('../Contact+3.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f, quotechar='"', delimiter=',')
            rows = list(reader)
            print(f"Method 4 (manual csv reader): {len(rows)-1} data rows")
            
            # Check for problematic rows
            for i, row in enumerate(rows[:10]):
                print(f"Row {i}: {len(row)} columns")
                if len(row) != 10:  # Expected 10 columns
                    print(f"  Problem row: {row}")
                    
    except Exception as e:
        print(f"Method 4 failed: {str(e)}")
    
    # Method 5: Check file encoding and line endings
    try:
        with open('../Contact+3.csv', 'rb') as f:
            first_bytes = f.read(1000)
            print(f"First 1000 bytes: {first_bytes}")
            
        # Count actual lines
        with open('../Contact+3.csv', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f"Total lines in file: {len(lines)}")
            
    except Exception as e:
        print(f"Method 5 failed: {str(e)}")

if __name__ == "__main__":
    test_csv_parsing()
