#!/usr/bin/env python3

"""
Import Manual Review Invoices Script for Ocean Soul Sparkles
Phase 2A: Import the remaining 5 invoices that require manual review

This script imports the remaining invoices that had fuzzy matches
with confidence scores between 0.70-0.79.
"""

import json
import requests
import logging
from datetime import datetime
from typing import Dict, List
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('manual-review-import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ManualReviewImporter:
    def __init__(self):
        self.supabase_url = os.getenv('NEXT_PUBLIC_SUPABASE_URL', '').strip()
        self.supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY', '').strip()
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Missing Supabase environment variables")
        
        self.headers = {
            'apikey': self.supabase_key.strip(),
            'Authorization': f'Bearer {self.supabase_key.strip()}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
        
        self.imported_invoices = []
        self.failed_invoices = []

    def load_manual_review_cases(self) -> List[Dict]:
        """Load manual review cases from JSON file."""
        try:
            with open('manual-review-invoices.json', 'r') as f:
                cases = json.load(f)
            logger.info(f"Loaded {len(cases)} manual review cases")
            return cases
        except Exception as e:
            logger.error(f"Failed to load manual review cases: {str(e)}")
            return []

    def filter_importable_cases(self, cases: List[Dict]) -> List[Dict]:
        """Filter cases that can be safely imported (fuzzy matches with reasonable confidence)."""
        importable = []
        
        for case in cases:
            # Import fuzzy matches with confidence >= 0.70
            if (case['match_type'] in ['fuzzy_medium', 'fuzzy_high'] and 
                case['confidence'] >= 0.70 and 
                case['suggested_customer_id']):
                importable.append(case)
        
        logger.info(f"Found {len(importable)} importable cases from manual review")
        return importable

    def import_invoice(self, case: Dict) -> bool:
        """Import a single invoice from manual review."""
        try:
            invoice_data = case['invoice_data'].copy()
            
            # Use the suggested customer ID
            invoice_data['customer_id'] = case['suggested_customer_id']
            
            # Update notes to reflect manual review approval
            original_notes = invoice_data.get('notes', '')
            invoice_data['notes'] = f"{original_notes} [Manual review: Approved fuzzy match]"
            
            response = requests.post(
                f"{self.supabase_url}/rest/v1/invoices",
                headers=self.headers,
                json=invoice_data
            )
            
            if response.status_code in [200, 201]:
                logger.info(f"Imported invoice {invoice_data['invoice_number']} for {case['customer_name']} (confidence: {case['confidence']:.2f})")
                self.imported_invoices.append({
                    'invoice_number': invoice_data['invoice_number'],
                    'customer_name': case['customer_name'],
                    'confidence': case['confidence'],
                    'amount': invoice_data['amount']
                })
                return True
            else:
                logger.error(f"Failed to import invoice {invoice_data['invoice_number']}: {response.text}")
                self.failed_invoices.append({
                    'invoice_number': invoice_data['invoice_number'],
                    'customer_name': case['customer_name'],
                    'error': response.text
                })
                return False
                
        except Exception as e:
            logger.error(f"Error importing invoice: {str(e)}")
            self.failed_invoices.append({
                'invoice_number': case.get('invoice_data', {}).get('invoice_number', 'unknown'),
                'customer_name': case.get('customer_name', 'unknown'),
                'error': str(e)
            })
            return False

    def import_manual_review_invoices(self) -> bool:
        """Main function to import all manual review invoices."""
        logger.info("Starting manual review invoice import process...")
        
        # Load manual review cases
        cases = self.load_manual_review_cases()
        if not cases:
            logger.error("No manual review cases found")
            return False
        
        # Filter importable cases
        importable_cases = self.filter_importable_cases(cases)
        if not importable_cases:
            logger.info("No importable cases found in manual review")
            return True
        
        logger.info(f"Importing {len(importable_cases)} invoices from manual review...")
        
        # Import each case
        for case in importable_cases:
            self.import_invoice(case)
        
        # Generate report
        self.generate_report()
        
        return True

    def generate_report(self):
        """Generate a comprehensive report of manual review import."""
        total_attempted = len(self.imported_invoices) + len(self.failed_invoices)
        success_rate = (len(self.imported_invoices) / total_attempted * 100) if total_attempted > 0 else 0
        
        report = f"""
# Manual Review Invoice Import Report
**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Status:** COMPLETED

## Summary
- **Total invoices attempted:** {total_attempted}
- **Successfully imported:** {len(self.imported_invoices)}
- **Failed to import:** {len(self.failed_invoices)}
- **Success rate:** {success_rate:.1f}%

## Successfully Imported Invoices
"""
        
        total_amount = 0
        for invoice in self.imported_invoices:
            report += f"- **{invoice['invoice_number']}** - {invoice['customer_name']} (${invoice['amount']:.2f}, confidence: {invoice['confidence']:.2f})\n"
            total_amount += invoice['amount']
        
        report += f"\n**Total value imported:** ${total_amount:.2f}\n"
        
        if self.failed_invoices:
            report += f"\n## Failed Invoice Imports\n"
            for invoice in self.failed_invoices:
                report += f"- **{invoice['invoice_number']}** - {invoice['customer_name']}: {invoice['error']}\n"
        
        report += f"""
## Final Invoice Import Statistics
- **Phase 1:** 15 invoices imported (exact matches)
- **Phase 2:** 53 invoices imported (after customer creation)
- **Phase 3:** {len(self.imported_invoices)} invoices imported (manual review)
- **Total imported:** {68 + len(self.imported_invoices)} out of 73 invoices
- **Overall success rate:** {((68 + len(self.imported_invoices)) / 73 * 100):.1f}%

## Next Steps
1. Validate all imported invoice data
2. Proceed with contact inquiry import
3. Proceed with email campaign import
4. Complete Phase 2 data migration
"""
        
        with open('manual-review-import-report.md', 'w') as f:
            f.write(report)
        
        logger.info("Generated manual review import report")
        print(report)

def main():
    """Main execution function."""
    importer = ManualReviewImporter()
    
    if not importer.import_manual_review_invoices():
        logger.error("Manual review import process failed.")
        return False
    
    logger.info("Manual review invoice import completed successfully!")
    return True

if __name__ == "__main__":
    main()
