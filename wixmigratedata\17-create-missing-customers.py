#!/usr/bin/env python3

"""
Create Missing Customers Script for Ocean Soul Sparkles
Phase 2A: Create customers for invoices that couldn't be matched

This script analyzes the manual review cases and creates missing customers
so that the remaining invoices can be imported.
"""

import json
import requests
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Optional
import os
from dotenv import load_dotenv
import re

# Load environment variables
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('create-missing-customers.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MissingCustomerCreator:
    def __init__(self):
        self.supabase_url = os.getenv('NEXT_PUBLIC_SUPABASE_URL', '').strip()
        self.supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY', '').strip()

        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Missing Supabase environment variables")

        self.headers = {
            'apikey': self.supabase_key.strip(),
            'Authorization': f'Bearer {self.supabase_key.strip()}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }

        self.created_customers = []
        self.failed_customers = []

    def load_manual_review_cases(self) -> List[Dict]:
        """Load manual review cases from JSON file."""
        try:
            with open('manual-review-invoices.json', 'r') as f:
                cases = json.load(f)
            logger.info(f"Loaded {len(cases)} manual review cases")
            return cases
        except Exception as e:
            logger.error(f"Failed to load manual review cases: {str(e)}")
            return []

    def analyze_missing_customers(self, cases: List[Dict]) -> List[str]:
        """Analyze cases to identify unique missing customers."""
        missing_customers = set()

        for case in cases:
            if case['match_type'] == 'no_match' and case['customer_name']:
                missing_customers.add(case['customer_name'])

        logger.info(f"Found {len(missing_customers)} unique missing customers")
        return list(missing_customers)

    def create_customer_record(self, customer_name: str) -> Dict:
        """Create a customer record from the name."""
        # Check if it's an email address first
        if '@' in customer_name:
            email = customer_name.lower()
            first_name = customer_name.split('@')[0]
            last_name = ''
        else:
            # Try to parse name into first/last
            name_parts = customer_name.strip().split()

            if len(name_parts) >= 2:
                first_name = name_parts[0]
                last_name = ' '.join(name_parts[1:])
            else:
                first_name = customer_name
                last_name = ''

            # Generate a placeholder email for customers without emails
            # Use a safe domain that won't conflict with real emails
            safe_name = re.sub(r'[^a-zA-Z0-9]', '', customer_name.lower())[:20]
            email = f"{safe_name}@migration.oceansoulsparkles.local"

        # Check if it's a company (contains business indicators)
        is_company = any(indicator in customer_name.lower() for indicator in [
            'pty', 'ltd', 'group', 'council', 'university', 'school',
            'entertainment', 'club', 'collective', 'hub', 'college'
        ])

        record = {
            'name': customer_name,
            'first_name': first_name,
            'last_name': last_name,
            'email': email,
            'notes': f'Auto-created during Wix invoice migration on {datetime.now().strftime("%Y-%m-%d")}. Type: {"business" if is_company else "individual"}'
        }

        # Remove None values
        return {k: v for k, v in record.items() if v is not None}

    def create_customer(self, customer_name: str) -> Optional[str]:
        """Create a single customer and return the ID."""
        try:
            customer_record = self.create_customer_record(customer_name)

            response = requests.post(
                f"{self.supabase_url}/rest/v1/customers",
                headers=self.headers,
                json=customer_record
            )

            if response.status_code in [200, 201]:
                created_customer = response.json()
                if isinstance(created_customer, list) and len(created_customer) > 0:
                    customer_id = created_customer[0]['id']
                else:
                    customer_id = created_customer['id']

                logger.info(f"Created customer: {customer_name} (ID: {customer_id})")
                self.created_customers.append({
                    'name': customer_name,
                    'id': customer_id,
                    'record': customer_record
                })
                return customer_id
            else:
                logger.error(f"Failed to create customer {customer_name}: {response.text}")
                self.failed_customers.append({
                    'name': customer_name,
                    'error': response.text
                })
                return None

        except Exception as e:
            logger.error(f"Error creating customer {customer_name}: {str(e)}")
            self.failed_customers.append({
                'name': customer_name,
                'error': str(e)
            })
            return None

    def create_missing_customers(self) -> bool:
        """Main function to create all missing customers."""
        logger.info("Starting missing customer creation process...")

        # Load manual review cases
        cases = self.load_manual_review_cases()
        if not cases:
            logger.error("No manual review cases found")
            return False

        # Analyze missing customers
        missing_customers = self.analyze_missing_customers(cases)
        if not missing_customers:
            logger.info("No missing customers to create")
            return True

        logger.info(f"Creating {len(missing_customers)} missing customers...")

        # Create each customer
        for customer_name in missing_customers:
            self.create_customer(customer_name)

        # Generate report
        self.generate_report()

        return True

    def generate_report(self):
        """Generate a comprehensive report of customer creation."""
        report = f"""
# Missing Customer Creation Report
**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Status:** COMPLETED

## Summary
- **Total customers to create:** {len(self.created_customers) + len(self.failed_customers)}
- **Successfully created:** {len(self.created_customers)}
- **Failed to create:** {len(self.failed_customers)}
- **Success rate:** {(len(self.created_customers) / (len(self.created_customers) + len(self.failed_customers)) * 100):.1f}%

## Successfully Created Customers
"""

        for customer in self.created_customers:
            report += f"- **{customer['name']}** (ID: {customer['id']})\n"

        if self.failed_customers:
            report += f"\n## Failed Customer Creations\n"
            for customer in self.failed_customers:
                report += f"- **{customer['name']}**: {customer['error']}\n"

        report += f"""
## Next Steps
1. Re-run the invoice import script to process remaining invoices
2. Review any failed customer creations
3. Validate customer data accuracy
4. Proceed with contact inquiry and email campaign import
"""

        with open('missing-customer-creation-report.md', 'w') as f:
            f.write(report)

        logger.info("Generated customer creation report")
        print(report)

def main():
    """Main execution function."""
    creator = MissingCustomerCreator()

    if not creator.create_missing_customers():
        logger.error("Customer creation process failed.")
        return False

    logger.info("Missing customer creation completed successfully!")
    return True

if __name__ == "__main__":
    main()
