#!/usr/bin/env python3

"""
Fix Contact+3.csv file to handle multiline messages properly
"""

import re

def fix_csv_file():
    """Fix the CSV file by properly escaping newlines in quoted fields."""
    
    print("Reading original CSV file...")
    with open('../Contact+3.csv', 'r', encoding='utf-8-sig') as f:
        content = f.read()
    
    print(f"Original file size: {len(content)} characters")
    
    # Split into lines
    lines = content.split('\n')
    print(f"Original lines: {len(lines)}")
    
    # Process the file to fix multiline quoted fields
    fixed_lines = []
    current_line = ""
    in_quoted_field = False
    quote_count = 0
    
    for line_num, line in enumerate(lines):
        if line_num == 0:
            # Header line - add as is
            fixed_lines.append(line)
            continue
        
        if not line.strip():
            continue
        
        # Count quotes in the line to determine if we're in a multiline field
        quote_count = line.count('"')
        
        # If we have an odd number of quotes, we're starting or ending a multiline field
        if current_line:
            # We're continuing a multiline field
            current_line += "\\n" + line  # Escape the newline
            
            # Check if this line ends the multiline field
            if quote_count % 2 == 1:  # Odd number of quotes means field ends
                fixed_lines.append(current_line)
                current_line = ""
        else:
            # Check if this line starts a multiline field
            if quote_count % 2 == 1:  # Odd number of quotes means field continues
                current_line = line
            else:
                # Complete line
                fixed_lines.append(line)
    
    # Add any remaining line
    if current_line:
        fixed_lines.append(current_line)
    
    print(f"Fixed lines: {len(fixed_lines)}")
    
    # Write the fixed file
    fixed_content = '\n'.join(fixed_lines)
    with open('../Contact+3-fixed.csv', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Fixed file written: {len(fixed_content)} characters")
    print("Fixed file saved as Contact+3-fixed.csv")

if __name__ == "__main__":
    fix_csv_file()
