#!/usr/bin/env python3
"""
Ocean Soul Sparkles - Import Remaining Data
Script to import bookings, invoices, and email campaigns after customers are already imported.
"""

import sys
import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import requests
import json
import uuid
from typing import Dict, List, Optional, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('remaining-data-migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://ndlgbcsbidyhxbpqzgqp.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY"

class RemainingDataImporter:
    """Import remaining data after customers are already in the database."""

    def __init__(self):
        self.supabase_url = SUPABASE_URL.rstrip('/')
        self.supabase_key = SUPABASE_KEY
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
        self.customer_lookup = {}

    def load_existing_customers(self):
        """Load existing customers from database for lookup."""
        try:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/customers?select=id,email",
                headers=self.headers
            )

            if response.status_code == 200:
                customers = response.json()
                for customer in customers:
                    if customer.get('email'):
                        self.customer_lookup[customer['email']] = customer['id']
                logger.info(f"Loaded {len(customers)} existing customers for lookup")
            else:
                logger.warning(f"Failed to load existing customers: {response.text}")

        except Exception as e:
            logger.error(f"Error loading existing customers: {str(e)}")

    def load_cleaned_data(self):
        """Load the cleaned CSV files."""
        cleaned_data = {}

        csv_files = {
            'bookings': 'cleaned_bookings.csv',
            'invoices': 'cleaned_invoices.csv',
            'corporate_emails': 'cleaned_corporate_emails.csv'
        }

        for key, filename in csv_files.items():
            try:
                if os.path.exists(filename):
                    df = pd.read_csv(filename)
                    cleaned_data[key] = df
                    logger.info(f"Loaded {len(df)} records from {filename}")
                else:
                    logger.warning(f"File not found: {filename}")
            except Exception as e:
                logger.error(f"Error loading {filename}: {str(e)}")

        return cleaned_data

    def clean_text_field(self, value: Any) -> Optional[str]:
        """Clean text field values."""
        if pd.isna(value) or value == '' or value == 'nan':
            return None
        return str(value).strip()

    def parse_date(self, value: Any) -> Optional[str]:
        """Parse date values to ISO format."""
        if pd.isna(value) or value == '':
            return None

        try:
            if isinstance(value, str):
                for fmt in ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%d %H:%M:%S']:
                    try:
                        parsed_date = datetime.strptime(value, fmt).date()
                        return parsed_date.isoformat()
                    except ValueError:
                        continue
            elif hasattr(value, 'date'):
                return value.date().isoformat()
            elif hasattr(value, 'isoformat'):
                return value.isoformat()
        except Exception as e:
            logger.warning(f"Failed to parse date '{value}': {e}")

        return None

    def parse_datetime(self, value: Any) -> Optional[str]:
        """Parse datetime values to ISO format."""
        if pd.isna(value) or value == '':
            return None

        try:
            if isinstance(value, str):
                for fmt in ['%Y-%m-%d %H:%M:%S', '%m/%d/%Y %H:%M:%S', '%Y-%m-%dT%H:%M:%S']:
                    try:
                        parsed_dt = datetime.strptime(value, fmt)
                        return parsed_dt.isoformat()
                    except ValueError:
                        continue
            elif hasattr(value, 'isoformat'):
                return value.isoformat()
        except Exception as e:
            logger.warning(f"Failed to parse datetime '{value}': {e}")

        return None

    def parse_decimal(self, value: Any) -> Optional[float]:
        """Parse decimal values."""
        if pd.isna(value) or value == '':
            return None

        try:
            if isinstance(value, str):
                value = value.replace('$', '').replace(',', '').replace('A$', '').strip()
            return float(value)
        except (ValueError, TypeError):
            return None

    def parse_integer(self, value: Any) -> Optional[int]:
        """Parse integer values."""
        if pd.isna(value) or value == '':
            return None

        try:
            return int(float(value))
        except (ValueError, TypeError):
            return None

    def map_booking_status(self, value: Any) -> str:
        """Map booking status values."""
        if pd.isna(value) or value == '':
            return 'pending'

        value_str = str(value).lower()
        status_mapping = {
            'confirmed': 'confirmed',
            'pending approval': 'pending',
            'pending': 'pending',
            'canceled': 'canceled',
            'cancelled': 'canceled',
            'declined': 'canceled',
            'completed': 'completed',
            'incomplete': 'in_progress',
            'in progress': 'in_progress',
            'no show': 'no_show',
            'rescheduled': 'rescheduled'
        }

        return status_mapping.get(value_str, 'pending')

    def map_payment_status(self, value: Any) -> str:
        """Map payment status values."""
        if pd.isna(value) or value == '':
            return 'not_paid'

        value_str = str(value).lower()
        if 'paid' in value_str:
            return 'paid'
        elif 'partial' in value_str:
            return 'partial'
        elif 'refunded' in value_str:
            return 'refunded'
        else:
            return 'not_paid'

    def prepare_booking_data(self, cleaned_data) -> List[Dict]:
        """Prepare booking data for Supabase import."""
        records = []

        if 'bookings' in cleaned_data and not cleaned_data['bookings'].empty:
            df = cleaned_data['bookings']
            for _, row in df.iterrows():
                email = self.clean_text_field(row.get('Email') or row.get('email'))
                customer_id = self.customer_lookup.get(email)

                if not customer_id:
                    logger.warning(f"No customer found for booking with email: {email}")
                    continue

                # Handle required start_time and end_time
                start_time = self.parse_datetime(row.get('Booking Start Time'))
                end_time = self.parse_datetime(row.get('Booking End Time'))

                # If no valid times, create default times
                if not start_time:
                    booking_date = self.parse_date(row.get('Booking Start Time'))
                    if booking_date:
                        start_time = f"{booking_date}T10:00:00"
                    else:
                        start_time = datetime.now().isoformat()

                if not end_time:
                    if start_time:
                        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        end_dt = start_dt + timedelta(hours=2)
                        end_time = end_dt.isoformat()
                    else:
                        end_time = datetime.now().isoformat()

                # Generate unique order number to avoid duplicates
                order_number = self.clean_text_field(row.get('Order Number'))
                unique_id = str(uuid.uuid4())[:8]
                if order_number:
                    order_number = f"WIX-{order_number}-{unique_id}"
                else:
                    order_number = f"WIX-{unique_id}"

                record = {
                    'id': str(uuid.uuid4()),
                    'customer_id': customer_id,
                    'start_time': start_time,
                    'end_time': end_time,
                    'status': self.map_booking_status(row.get('Booking Status')),
                    'order_number': order_number,
                    'booking_date': self.parse_date(row.get('Booking Start Time')),
                    'payment_status': self.map_payment_status(row.get('Payment Status')),
                    'total_amount': self.parse_decimal(row.get('Order Total')),
                    'location_address': self.clean_text_field(row.get('Location Address')),
                    'staff_member': self.clean_text_field(row.get('Staff Member')),
                    'group_size': self.parse_integer(row.get('Group Size')),
                    'event_name': self.clean_text_field(row.get('Event Name')),
                    'notes': self.clean_text_field(row.get('Notes')),
                    'booking_source': 'wix_migration'
                }

                # Remove None values
                record = {k: v for k, v in record.items() if v is not None}
                records.append(record)

        logger.info(f"Prepared {len(records)} booking records for import")
        return records

    def insert_batch(self, table_name: str, records: List[Dict], batch_size: int = 50) -> tuple:
        """Insert records in batches."""
        success_count = 0
        error_count = 0
        errors = []

        # Normalize all records to have the same keys
        if records:
            all_keys = set()
            for record in records:
                all_keys.update(record.keys())

            # Ensure all records have all keys (with None for missing ones)
            normalized_records = []
            for record in records:
                normalized_record = {}
                for key in all_keys:
                    normalized_record[key] = record.get(key)
                normalized_records.append(normalized_record)

            records = normalized_records

        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]

            try:
                response = requests.post(
                    f"{self.supabase_url}/rest/v1/{table_name}",
                    headers=self.headers,
                    json=batch
                )

                if response.status_code in [200, 201]:
                    success_count += len(batch)
                    logger.info(f"Inserted batch {i//batch_size + 1}: {len(batch)} records into {table_name}")
                else:
                    error_count += len(batch)
                    error_msg = f"Batch {i//batch_size + 1} failed: {response.text}"
                    errors.append(error_msg)
                    logger.error(error_msg)

            except Exception as e:
                error_count += len(batch)
                error_msg = f"Batch {i//batch_size + 1} exception: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)

        return success_count, error_count, errors

    def execute_import(self):
        """Execute the remaining data import."""
        logger.info("=" * 60)
        logger.info("OCEAN SOUL SPARKLES - REMAINING DATA IMPORT")
        logger.info("=" * 60)

        # Load existing customers
        self.load_existing_customers()

        # Load cleaned data
        cleaned_data = self.load_cleaned_data()

        results = {}

        # Import bookings
        if 'bookings' in cleaned_data:
            logger.info("\nImporting bookings...")
            booking_records = self.prepare_booking_data(cleaned_data)
            if booking_records:
                success, errors, error_list = self.insert_batch('bookings', booking_records)
                results['bookings'] = {'imported': success, 'errors': errors}
                logger.info(f"Bookings: {success} imported, {errors} errors")

        logger.info("\n" + "=" * 60)
        logger.info("IMPORT COMPLETED")
        logger.info("=" * 60)

        return results

def main():
    """Main function."""
    try:
        importer = RemainingDataImporter()
        results = importer.execute_import()

        print("\n" + "=" * 60)
        print("IMPORT SUMMARY")
        print("=" * 60)
        for table, stats in results.items():
            print(f"{table.title()}: {stats['imported']} imported, {stats['errors']} errors")
        print("=" * 60)

        return results

    except Exception as e:
        logger.error(f"Import failed: {str(e)}")
        return None

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
