2025-05-27 11:09:40,791 - INFO - ============================================================
2025-05-27 11:09:40,792 - INFO - OCEAN SOUL SPARKLES - DATA MIGRATION EXECUTION
2025-05-27 11:09:40,793 - INFO - ============================================================
2025-05-27 11:09:40,793 - INFO - Migration started at: 2025-05-27 11:09:40.791576
2025-05-27 11:09:40,811 - INFO - --------------------------------------------------
2025-05-27 11:09:40,811 - INFO - Loading and cleaning CSV data files...
2025-05-27 11:09:40,819 - INFO - Starting comprehensive data cleaning process...
2025-05-27 11:09:40,820 - INFO - Processing contacts .Google csv...
2025-05-27 11:09:40,859 - INFO - Loaded 1022 records from contacts .Google csv
2025-05-27 11:09:40,870 - WARNING - Unable to standardize phone number: +41476488841
2025-05-27 11:09:40,870 - WARNING - Unable to standardize phone number: +93422771273
2025-05-27 11:09:40,870 - WARNING - Incomplete phone number requires manual review: 54436375
2025-05-27 11:09:40,870 - WARNING - Unable to standardize phone number: 04439595029
2025-05-27 11:09:40,871 - WARNING - Unable to standardize phone number: +447932773139
2025-05-27 11:09:40,871 - WARNING - Unable to standardize phone number: +81453196127
2025-05-27 11:09:40,872 - WARNING - Unable to standardize phone number: +919830152514
2025-05-27 11:09:40,873 - WARNING - Unable to standardize phone number: +612344953
2025-05-27 11:09:40,874 - INFO - Cleaned contacts .Google csv: 1022 records
2025-05-27 11:09:40,875 - INFO - Processing contacts Regular.csv...
2025-05-27 11:09:40,887 - INFO - Loaded 1022 records from contacts Regular.csv
2025-05-27 11:09:40,888 - INFO - Cleaned contacts Regular.csv: 1022 records
2025-05-27 11:09:40,889 - INFO - Processing Booking list-5_14_2025.csv...
2025-05-27 11:09:40,896 - INFO - Loaded 104 records from Booking list-5_14_2025.csv
2025-05-27 11:09:40,896 - INFO - Cleaning booking data...
2025-05-27 11:09:40,900 - INFO - Found 57 bookings with incomplete names
2025-05-27 11:09:40,900 - INFO - Found 17 bookings with incomplete emails
2025-05-27 11:09:40,900 - INFO - Found 23 bookings with incomplete phones
2025-05-27 11:09:40,900 - INFO - Cleaned Booking list-5_14_2025.csv: 104 records
2025-05-27 11:09:40,900 - INFO - Processing invoices.csv...
2025-05-27 11:09:40,902 - INFO - Loaded 73 records from invoices.csv
2025-05-27 11:09:40,914 - INFO - Cleaned invoices.csv: 73 records
2025-05-27 11:09:40,914 - INFO - Processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv...
2025-05-27 11:09:40,916 - ERROR - Error processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv: Error tokenizing data. C error: Expected 1 fields in line 4, saw 5

2025-05-27 11:09:40,916 - INFO - Processing catalog_products.csv...
2025-05-27 11:09:40,920 - INFO - Loaded 12 records from catalog_products.csv
2025-05-27 11:09:40,926 - INFO - Cleaned catalog_products.csv: 12 records
2025-05-27 11:09:40,926 - INFO - Processing Corporate emails.csv...
2025-05-27 11:09:40,928 - INFO - Loaded 39 records from Corporate emails.csv
2025-05-27 11:09:40,931 - INFO - Cleaned Corporate emails.csv: 39 records
2025-05-27 11:09:40,931 - INFO - Resolving customer duplicates...
2025-05-27 11:09:40,949 - INFO -    - Phone numbers standardized: 911
2025-05-27 11:09:40,949 - INFO -    - Email addresses validated: 1022
2025-05-27 11:09:40,949 - INFO -    - Duplicate customers identified: 0
2025-05-27 11:09:40,949 - INFO -    - Duplicates resolved: 0
2025-05-27 11:09:40,990 - INFO -    - Saved 1022 records to cleaned_google_contacts.csv
2025-05-27 11:09:41,003 - INFO -    - Saved 1022 records to cleaned_regular_contacts.csv
2025-05-27 11:09:41,007 - INFO -    - Saved 104 records to cleaned_bookings.csv
2025-05-27 11:09:41,010 - INFO -    - Saved 73 records to cleaned_invoices.csv
2025-05-27 11:09:41,014 - INFO -    - Saved 12 records to cleaned_products.csv
2025-05-27 11:09:41,016 - INFO -    - Saved 39 records to cleaned_corporate_emails.csv
2025-05-27 11:09:41,018 - INFO - --------------------------------------------------
2025-05-27 11:09:41,018 - INFO - Resolving critical duplicate customers...
2025-05-27 11:09:41,021 - INFO -    - Jessica Endsor: Merged business and personal emails
2025-05-27 11:09:41,021 - INFO -    - Electric Lady Land/Matt Ambler: Consolidated business contact
2025-05-27 11:09:41,021 - INFO -    - Kate - Virtue & Vice: Corrected email typos and merged
2025-05-27 11:09:41,022 - INFO - --------------------------------------------------
2025-05-27 11:09:41,022 - INFO - Starting data import to Supabase...
2025-05-27 11:09:41,167 - INFO - Prepared 989 customer records for import
2025-05-27 11:09:41,656 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,727 - ERROR - Batch 2 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,796 - ERROR - Batch 3 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,857 - ERROR - Batch 4 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,922 - ERROR - Batch 5 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:41,984 - ERROR - Batch 6 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,046 - ERROR - Batch 7 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,108 - ERROR - Batch 8 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,169 - ERROR - Batch 9 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,234 - ERROR - Batch 10 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,240 - INFO - Prepared 12 product records for import
2025-05-27 11:09:42,307 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,311 - INFO - Prepared 0 contact inquiry records for import
2025-05-27 11:09:42,315 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,317 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,318 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,319 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,319 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,319 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,320 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,320 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,320 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,320 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,321 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,322 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,322 - WARNING - No customer found for booking with email: None
2025-05-27 11:09:42,324 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,324 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,324 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,325 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,331 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,331 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,332 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,332 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,332 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,338 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,340 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,341 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,341 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:09:42,344 - INFO - Prepared 73 booking records for import
2025-05-27 11:09:42,402 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,411 - INFO - Prepared 73 invoice records for import
2025-05-27 11:09:42,458 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,465 - INFO - Prepared 39 email campaign records for import
2025-05-27 11:09:42,515 - ERROR - Batch 1 failed: {"message":"Invalid API key","hint":"Double check your Supabase `anon` or `service_role` API key."}
2025-05-27 11:09:42,517 - INFO - --------------------------------------------------
2025-05-27 11:09:42,517 - INFO - Executing validation checks...
2025-05-27 11:09:42,672 - INFO - Validation Results:
2025-05-27 11:09:42,704 - INFO - --------------------------------------------------
2025-05-27 11:13:12,054 - INFO - ============================================================
2025-05-27 11:13:12,055 - INFO - OCEAN SOUL SPARKLES - DATA MIGRATION EXECUTION
2025-05-27 11:13:12,055 - INFO - ============================================================
2025-05-27 11:13:12,056 - INFO - Migration started at: 2025-05-27 11:13:12.054786
2025-05-27 11:13:12,056 - INFO - 
[PHASE 1] DATA CLEANING AND PREPARATION
2025-05-27 11:13:12,056 - INFO - --------------------------------------------------
2025-05-27 11:13:12,056 - INFO - Loading and cleaning CSV data files...
2025-05-27 11:13:12,057 - INFO - Starting comprehensive data cleaning process...
2025-05-27 11:13:12,057 - INFO - Processing contacts .Google csv...
2025-05-27 11:13:12,076 - INFO - Loaded 1022 records from contacts .Google csv
2025-05-27 11:13:12,082 - WARNING - Unable to standardize phone number: +41476488841
2025-05-27 11:13:12,082 - WARNING - Unable to standardize phone number: +93422771273
2025-05-27 11:13:12,082 - WARNING - Incomplete phone number requires manual review: 54436375
2025-05-27 11:13:12,082 - WARNING - Unable to standardize phone number: 04439595029
2025-05-27 11:13:12,083 - WARNING - Unable to standardize phone number: +447932773139
2025-05-27 11:13:12,084 - WARNING - Unable to standardize phone number: +81453196127
2025-05-27 11:13:12,084 - WARNING - Unable to standardize phone number: +919830152514
2025-05-27 11:13:12,086 - WARNING - Unable to standardize phone number: +612344953
2025-05-27 11:13:12,088 - INFO - Cleaned contacts .Google csv: 1022 records
2025-05-27 11:13:12,088 - INFO - Processing contacts Regular.csv...
2025-05-27 11:13:12,098 - INFO - Loaded 1022 records from contacts Regular.csv
2025-05-27 11:13:12,100 - INFO - Cleaned contacts Regular.csv: 1022 records
2025-05-27 11:13:12,100 - INFO - Processing Booking list-5_14_2025.csv...
2025-05-27 11:13:12,105 - INFO - Loaded 104 records from Booking list-5_14_2025.csv
2025-05-27 11:13:12,105 - INFO - Cleaning booking data...
2025-05-27 11:13:12,107 - INFO - Found 57 bookings with incomplete names
2025-05-27 11:13:12,107 - INFO - Found 17 bookings with incomplete emails
2025-05-27 11:13:12,108 - INFO - Found 23 bookings with incomplete phones
2025-05-27 11:13:12,108 - INFO - Cleaned Booking list-5_14_2025.csv: 104 records
2025-05-27 11:13:12,108 - INFO - Processing invoices.csv...
2025-05-27 11:13:12,109 - INFO - Loaded 73 records from invoices.csv
2025-05-27 11:13:12,115 - INFO - Cleaned invoices.csv: 73 records
2025-05-27 11:13:12,115 - INFO - Processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv...
2025-05-27 11:13:12,115 - ERROR - Error processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv: Error tokenizing data. C error: Expected 1 fields in line 4, saw 5

2025-05-27 11:13:12,116 - INFO - Processing catalog_products.csv...
2025-05-27 11:13:12,120 - INFO - Loaded 12 records from catalog_products.csv
2025-05-27 11:13:12,122 - INFO - Cleaned catalog_products.csv: 12 records
2025-05-27 11:13:12,123 - INFO - Processing Corporate emails.csv...
2025-05-27 11:13:12,124 - INFO - Loaded 39 records from Corporate emails.csv
2025-05-27 11:13:12,125 - INFO - Cleaned Corporate emails.csv: 39 records
2025-05-27 11:13:12,125 - INFO - Resolving customer duplicates...
2025-05-27 11:13:12,137 - INFO - [SUCCESS] Data cleaning completed successfully
2025-05-27 11:13:12,138 - INFO -    - Phone numbers standardized: 911
2025-05-27 11:13:12,138 - INFO -    - Email addresses validated: 1022
2025-05-27 11:13:12,138 - INFO -    - Duplicate customers identified: 0
2025-05-27 11:13:12,138 - INFO -    - Duplicates resolved: 0
2025-05-27 11:13:12,176 - INFO -    - Saved 1022 records to cleaned_google_contacts.csv
2025-05-27 11:13:12,189 - INFO -    - Saved 1022 records to cleaned_regular_contacts.csv
2025-05-27 11:13:12,192 - INFO -    - Saved 104 records to cleaned_bookings.csv
2025-05-27 11:13:12,194 - INFO -    - Saved 73 records to cleaned_invoices.csv
2025-05-27 11:13:12,197 - INFO -    - Saved 12 records to cleaned_products.csv
2025-05-27 11:13:12,198 - INFO -    - Saved 39 records to cleaned_corporate_emails.csv
2025-05-27 11:13:12,198 - INFO - 
[PHASE 2] DUPLICATE RESOLUTION
2025-05-27 11:13:12,199 - INFO - --------------------------------------------------
2025-05-27 11:13:12,199 - INFO - Resolving critical duplicate customers...
2025-05-27 11:13:12,199 - INFO - [SUCCESS] Applied business rules for critical duplicates:
2025-05-27 11:13:12,199 - INFO -    - Jessica Endsor: Merged business and personal emails
2025-05-27 11:13:12,199 - INFO -    - Electric Lady Land/Matt Ambler: Consolidated business contact
2025-05-27 11:13:12,199 - INFO -    - Kate - Virtue & Vice: Corrected email typos and merged
2025-05-27 11:13:12,199 - INFO - 
[PHASE 3] DATA IMPORT TO SUPABASE
2025-05-27 11:13:12,199 - INFO - --------------------------------------------------
2025-05-27 11:13:12,199 - INFO - Starting data import to Supabase...
2025-05-27 11:13:12,338 - INFO - Prepared 989 customer records for import
2025-05-27 11:13:13,493 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:13,820 - ERROR - Batch 2 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:14,155 - ERROR - Batch 3 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:14,456 - ERROR - Batch 4 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:14,600 - ERROR - Batch 5 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:14,820 - ERROR - Batch 6 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,021 - ERROR - Batch 7 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,291 - ERROR - Batch 8 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,432 - ERROR - Batch 9 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,616 - ERROR - Batch 10 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,616 - INFO - [SUCCESS] Customers: 0 imported, 989 errors
2025-05-27 11:13:15,618 - INFO - Prepared 12 product records for import
2025-05-27 11:13:15,747 - ERROR - Batch 1 failed: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'cost' column of 'products' in the schema cache"}
2025-05-27 11:13:15,747 - INFO - [SUCCESS] Products: 0 imported, 12 errors
2025-05-27 11:13:15,748 - INFO - Prepared 0 contact inquiry records for import
2025-05-27 11:13:15,748 - INFO - [INFO] No contact inquiry data to import
2025-05-27 11:13:15,750 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,752 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,752 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,752 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,752 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,753 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,754 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,755 - WARNING - No customer found for booking with email: None
2025-05-27 11:13:15,755 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,755 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,756 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,756 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,758 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,758 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,758 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,758 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,759 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,764 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,765 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,767 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,767 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:13:15,770 - INFO - Prepared 73 booking records for import
2025-05-27 11:13:15,856 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:15,857 - INFO - [SUCCESS] Bookings: 0 imported, 73 errors
2025-05-27 11:13:15,864 - INFO - Prepared 73 invoice records for import
2025-05-27 11:13:16,161 - ERROR - Batch 1 failed: {"code":"23502","details":"Failing row contains (07c41d5f-f0de-4ec3-beec-049dae499906, null, null, null, null, null, 0.00, AUD, null, null, draft, null, 2025-05-27 01:13:13.63265+00, 2025-05-27 01:13:13.63265+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"invoice_number\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 11:13:16,162 - INFO - [SUCCESS] Invoices: 0 imported, 73 errors
2025-05-27 11:13:16,166 - INFO - Prepared 39 email campaign records for import
2025-05-27 11:13:16,239 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:13:16,239 - INFO - [SUCCESS] Email Campaigns: 0 imported, 39 errors
2025-05-27 11:13:16,239 - INFO - 
[PHASE 4] VALIDATION AND VERIFICATION
2025-05-27 11:13:16,239 - INFO - --------------------------------------------------
2025-05-27 11:13:16,240 - INFO - Executing validation checks...
2025-05-27 11:13:16,599 - INFO - Validation Results:
2025-05-27 11:13:16,599 - INFO -    [DATA] Customers imported: 1
2025-05-27 11:13:16,599 - INFO -    [DATA] Bookings imported: 1
2025-05-27 11:13:16,600 - INFO -    [DATA] Invoices imported: 1
2025-05-27 11:13:16,600 - INFO -    [DATA] Total records imported: 0
2025-05-27 11:13:16,600 - INFO -    [DATA] Total errors: 1186
2025-05-27 11:13:16,600 - INFO -    [FAIL] Customer Data Accuracy: 0.1%
2025-05-27 11:13:16,600 - INFO -    [PASS] Financial Reconciliation: 100.0%
2025-05-27 11:13:16,600 - INFO -    [FAIL] Duplicate Customer Rate: 0.5%
2025-05-27 11:13:16,600 - INFO -    [PASS] Email Validity Rate: 98.0%
2025-05-27 11:13:16,601 - INFO -    [PASS] Phone Standardization: 100.0%
2025-05-27 11:13:16,601 - INFO -    [FAIL] Data Completeness: 0.0%
2025-05-27 11:13:16,601 - INFO -    [PASS] Foreign Key Integrity: 100.0%
2025-05-27 11:13:16,601 - WARNING - [FAIL] customer_data_accuracy (0.1%) below threshold (99.0%)
2025-05-27 11:13:16,601 - WARNING - [WARNING] Some success criteria not met - review required
2025-05-27 11:13:16,601 - INFO - 
[PHASE 5] FINAL REPORTING
2025-05-27 11:13:16,601 - INFO - --------------------------------------------------
2025-05-27 11:13:16,602 - ERROR - [ERROR] MIGRATION FAILED: 'charmap' codec can't encode characters in position 201-202: character maps to <undefined>
2025-05-27 11:23:00,848 - INFO - ============================================================
2025-05-27 11:23:00,849 - INFO - OCEAN SOUL SPARKLES - DATA MIGRATION EXECUTION
2025-05-27 11:23:00,849 - INFO - ============================================================
2025-05-27 11:23:00,850 - INFO - Migration started at: 2025-05-27 11:23:00.848561
2025-05-27 11:23:00,850 - INFO - 
[PHASE 1] DATA CLEANING AND PREPARATION
2025-05-27 11:23:00,850 - INFO - --------------------------------------------------
2025-05-27 11:23:00,850 - INFO - Loading and cleaning CSV data files...
2025-05-27 11:23:00,851 - INFO - Starting comprehensive data cleaning process...
2025-05-27 11:23:00,851 - INFO - Processing contacts .Google csv...
2025-05-27 11:23:00,870 - INFO - Loaded 1022 records from contacts .Google csv
2025-05-27 11:23:00,879 - WARNING - Unable to standardize phone number: +41476488841
2025-05-27 11:23:00,879 - WARNING - Unable to standardize phone number: +93422771273
2025-05-27 11:23:00,880 - WARNING - Incomplete phone number requires manual review: 54436375
2025-05-27 11:23:00,880 - WARNING - Unable to standardize phone number: 04439595029
2025-05-27 11:23:00,881 - WARNING - Unable to standardize phone number: +447932773139
2025-05-27 11:23:00,881 - WARNING - Unable to standardize phone number: +81453196127
2025-05-27 11:23:00,881 - WARNING - Unable to standardize phone number: +919830152514
2025-05-27 11:23:00,882 - WARNING - Unable to standardize phone number: +612344953
2025-05-27 11:23:00,883 - INFO - Cleaned contacts .Google csv: 1022 records
2025-05-27 11:23:00,884 - INFO - Processing contacts Regular.csv...
2025-05-27 11:23:00,894 - INFO - Loaded 1022 records from contacts Regular.csv
2025-05-27 11:23:00,896 - INFO - Cleaned contacts Regular.csv: 1022 records
2025-05-27 11:23:00,896 - INFO - Processing Booking list-5_14_2025.csv...
2025-05-27 11:23:00,901 - INFO - Loaded 104 records from Booking list-5_14_2025.csv
2025-05-27 11:23:00,901 - INFO - Cleaning booking data...
2025-05-27 11:23:00,903 - INFO - Found 57 bookings with incomplete names
2025-05-27 11:23:00,903 - INFO - Found 17 bookings with incomplete emails
2025-05-27 11:23:00,903 - INFO - Found 23 bookings with incomplete phones
2025-05-27 11:23:00,903 - INFO - Cleaned Booking list-5_14_2025.csv: 104 records
2025-05-27 11:23:00,903 - INFO - Processing invoices.csv...
2025-05-27 11:23:00,905 - INFO - Loaded 73 records from invoices.csv
2025-05-27 11:23:00,912 - INFO - Cleaned invoices.csv: 73 records
2025-05-27 11:23:00,912 - INFO - Processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv...
2025-05-27 11:23:00,913 - ERROR - Error processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv: Error tokenizing data. C error: Expected 1 fields in line 4, saw 5

2025-05-27 11:23:00,913 - INFO - Processing catalog_products.csv...
2025-05-27 11:23:00,917 - INFO - Loaded 12 records from catalog_products.csv
2025-05-27 11:23:00,920 - INFO - Cleaned catalog_products.csv: 12 records
2025-05-27 11:23:00,920 - INFO - Processing Corporate emails.csv...
2025-05-27 11:23:00,922 - INFO - Loaded 39 records from Corporate emails.csv
2025-05-27 11:23:00,924 - INFO - Cleaned Corporate emails.csv: 39 records
2025-05-27 11:23:00,924 - INFO - Resolving customer duplicates...
2025-05-27 11:23:00,937 - INFO - [SUCCESS] Data cleaning completed successfully
2025-05-27 11:23:00,937 - INFO -    - Phone numbers standardized: 911
2025-05-27 11:23:00,937 - INFO -    - Email addresses validated: 1022
2025-05-27 11:23:00,937 - INFO -    - Duplicate customers identified: 0
2025-05-27 11:23:00,937 - INFO -    - Duplicates resolved: 0
2025-05-27 11:23:00,976 - INFO -    - Saved 1022 records to cleaned_google_contacts.csv
2025-05-27 11:23:00,988 - INFO -    - Saved 1022 records to cleaned_regular_contacts.csv
2025-05-27 11:23:00,993 - INFO -    - Saved 104 records to cleaned_bookings.csv
2025-05-27 11:23:00,994 - INFO -    - Saved 73 records to cleaned_invoices.csv
2025-05-27 11:23:00,998 - INFO -    - Saved 12 records to cleaned_products.csv
2025-05-27 11:23:00,999 - INFO -    - Saved 39 records to cleaned_corporate_emails.csv
2025-05-27 11:23:00,999 - INFO - 
[PHASE 2] DUPLICATE RESOLUTION
2025-05-27 11:23:00,999 - INFO - --------------------------------------------------
2025-05-27 11:23:00,999 - INFO - Resolving critical duplicate customers...
2025-05-27 11:23:01,000 - INFO - [SUCCESS] Applied business rules for critical duplicates:
2025-05-27 11:23:01,000 - INFO -    - Jessica Endsor: Merged business and personal emails
2025-05-27 11:23:01,000 - INFO -    - Electric Lady Land/Matt Ambler: Consolidated business contact
2025-05-27 11:23:01,000 - INFO -    - Kate - Virtue & Vice: Corrected email typos and merged
2025-05-27 11:23:01,000 - INFO - 
[PHASE 3] DATA IMPORT TO SUPABASE
2025-05-27 11:23:01,000 - INFO - --------------------------------------------------
2025-05-27 11:23:01,000 - INFO - Starting data import to Supabase...
2025-05-27 11:23:01,146 - INFO - Prepared 989 customer records for import
2025-05-27 11:23:01,708 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:01,836 - ERROR - Batch 2 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,030 - ERROR - Batch 3 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,181 - ERROR - Batch 4 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,334 - ERROR - Batch 5 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,455 - ERROR - Batch 6 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,588 - ERROR - Batch 7 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,704 - ERROR - Batch 8 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,856 - ERROR - Batch 9 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,983 - ERROR - Batch 10 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:02,983 - INFO - [SUCCESS] Customers: 0 imported, 989 errors
2025-05-27 11:23:02,985 - INFO - Prepared 12 product records for import
2025-05-27 11:23:03,212 - INFO - Inserted batch 1: 12 records into products
2025-05-27 11:23:03,213 - INFO - [SUCCESS] Products: 12 imported, 0 errors
2025-05-27 11:23:03,213 - INFO - Prepared 0 contact inquiry records for import
2025-05-27 11:23:03,213 - INFO - [INFO] No contact inquiry data to import
2025-05-27 11:23:03,215 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,216 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,217 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,219 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,219 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,219 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,219 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,219 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,220 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,222 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,222 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,222 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,222 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,222 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,223 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,223 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,223 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,223 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:03,224 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,225 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,226 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,226 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,228 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,228 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,228 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,228 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,229 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,240 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,242 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,244 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,245 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:03,248 - INFO - Prepared 73 booking records for import
2025-05-27 11:23:03,362 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:03,363 - INFO - [SUCCESS] Bookings: 0 imported, 73 errors
2025-05-27 11:23:03,370 - INFO - Prepared 0 invoice records for import
2025-05-27 11:23:03,374 - INFO - Prepared 39 email campaign records for import
2025-05-27 11:23:03,465 - ERROR - Batch 1 failed: {"code":"PGRST102","details":null,"hint":null,"message":"All object keys must match"}
2025-05-27 11:23:03,465 - INFO - [SUCCESS] Email Campaigns: 0 imported, 39 errors
2025-05-27 11:23:03,466 - INFO - 
[PHASE 4] VALIDATION AND VERIFICATION
2025-05-27 11:23:03,466 - INFO - --------------------------------------------------
2025-05-27 11:23:03,466 - INFO - Executing validation checks...
2025-05-27 11:23:03,750 - INFO - Validation Results:
2025-05-27 11:23:03,751 - INFO -    [DATA] Customers imported: 1
2025-05-27 11:23:03,751 - INFO -    [DATA] Bookings imported: 1
2025-05-27 11:23:03,751 - INFO -    [DATA] Invoices imported: 1
2025-05-27 11:23:03,751 - INFO -    [DATA] Total records imported: 12
2025-05-27 11:23:03,751 - INFO -    [DATA] Total errors: 1101
2025-05-27 11:23:03,751 - INFO -    [FAIL] Customer Data Accuracy: 0.1%
2025-05-27 11:23:03,752 - INFO -    [PASS] Financial Reconciliation: 100.0%
2025-05-27 11:23:03,752 - INFO -    [FAIL] Duplicate Customer Rate: 0.5%
2025-05-27 11:23:03,752 - INFO -    [PASS] Email Validity Rate: 98.0%
2025-05-27 11:23:03,752 - INFO -    [PASS] Phone Standardization: 100.0%
2025-05-27 11:23:03,752 - INFO -    [FAIL] Data Completeness: 1.1%
2025-05-27 11:23:03,752 - INFO -    [PASS] Foreign Key Integrity: 100.0%
2025-05-27 11:23:03,752 - WARNING - [FAIL] customer_data_accuracy (0.1%) below threshold (99.0%)
2025-05-27 11:23:03,752 - WARNING - [WARNING] Some success criteria not met - review required
2025-05-27 11:23:03,753 - INFO - 
[PHASE 5] FINAL REPORTING
2025-05-27 11:23:03,753 - INFO - --------------------------------------------------
2025-05-27 11:23:03,754 - INFO - [INFO] Final report saved to: migration-completion-report.md
2025-05-27 11:23:03,754 - INFO - 
[SUCCESS] MIGRATION COMPLETED SUCCESSFULLY!
2025-05-27 11:23:03,754 - INFO - ============================================================
2025-05-27 11:23:37,744 - INFO - ============================================================
2025-05-27 11:23:37,744 - INFO - OCEAN SOUL SPARKLES - DATA MIGRATION EXECUTION
2025-05-27 11:23:37,745 - INFO - ============================================================
2025-05-27 11:23:37,745 - INFO - Migration started at: 2025-05-27 11:23:37.743985
2025-05-27 11:23:37,745 - INFO - 
[PHASE 1] DATA CLEANING AND PREPARATION
2025-05-27 11:23:37,745 - INFO - --------------------------------------------------
2025-05-27 11:23:37,745 - INFO - Loading and cleaning CSV data files...
2025-05-27 11:23:37,746 - INFO - Starting comprehensive data cleaning process...
2025-05-27 11:23:37,746 - INFO - Processing contacts .Google csv...
2025-05-27 11:23:37,764 - INFO - Loaded 1022 records from contacts .Google csv
2025-05-27 11:23:37,771 - WARNING - Unable to standardize phone number: +41476488841
2025-05-27 11:23:37,771 - WARNING - Unable to standardize phone number: +93422771273
2025-05-27 11:23:37,771 - WARNING - Incomplete phone number requires manual review: 54436375
2025-05-27 11:23:37,771 - WARNING - Unable to standardize phone number: 04439595029
2025-05-27 11:23:37,772 - WARNING - Unable to standardize phone number: +447932773139
2025-05-27 11:23:37,772 - WARNING - Unable to standardize phone number: +81453196127
2025-05-27 11:23:37,772 - WARNING - Unable to standardize phone number: +919830152514
2025-05-27 11:23:37,774 - WARNING - Unable to standardize phone number: +612344953
2025-05-27 11:23:37,775 - INFO - Cleaned contacts .Google csv: 1022 records
2025-05-27 11:23:37,775 - INFO - Processing contacts Regular.csv...
2025-05-27 11:23:37,785 - INFO - Loaded 1022 records from contacts Regular.csv
2025-05-27 11:23:37,786 - INFO - Cleaned contacts Regular.csv: 1022 records
2025-05-27 11:23:37,786 - INFO - Processing Booking list-5_14_2025.csv...
2025-05-27 11:23:37,791 - INFO - Loaded 104 records from Booking list-5_14_2025.csv
2025-05-27 11:23:37,791 - INFO - Cleaning booking data...
2025-05-27 11:23:37,794 - INFO - Found 57 bookings with incomplete names
2025-05-27 11:23:37,794 - INFO - Found 17 bookings with incomplete emails
2025-05-27 11:23:37,794 - INFO - Found 23 bookings with incomplete phones
2025-05-27 11:23:37,794 - INFO - Cleaned Booking list-5_14_2025.csv: 104 records
2025-05-27 11:23:37,794 - INFO - Processing invoices.csv...
2025-05-27 11:23:37,796 - INFO - Loaded 73 records from invoices.csv
2025-05-27 11:23:37,801 - INFO - Cleaned invoices.csv: 73 records
2025-05-27 11:23:37,801 - INFO - Processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv...
2025-05-27 11:23:37,802 - ERROR - Error processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv: Error tokenizing data. C error: Expected 1 fields in line 4, saw 5

2025-05-27 11:23:37,802 - INFO - Processing catalog_products.csv...
2025-05-27 11:23:37,806 - INFO - Loaded 12 records from catalog_products.csv
2025-05-27 11:23:37,810 - INFO - Cleaned catalog_products.csv: 12 records
2025-05-27 11:23:37,810 - INFO - Processing Corporate emails.csv...
2025-05-27 11:23:37,812 - INFO - Loaded 39 records from Corporate emails.csv
2025-05-27 11:23:37,813 - INFO - Cleaned Corporate emails.csv: 39 records
2025-05-27 11:23:37,813 - INFO - Resolving customer duplicates...
2025-05-27 11:23:37,827 - INFO - [SUCCESS] Data cleaning completed successfully
2025-05-27 11:23:37,828 - INFO -    - Phone numbers standardized: 911
2025-05-27 11:23:37,828 - INFO -    - Email addresses validated: 1022
2025-05-27 11:23:37,828 - INFO -    - Duplicate customers identified: 0
2025-05-27 11:23:37,828 - INFO -    - Duplicates resolved: 0
2025-05-27 11:23:37,866 - INFO -    - Saved 1022 records to cleaned_google_contacts.csv
2025-05-27 11:23:37,879 - INFO -    - Saved 1022 records to cleaned_regular_contacts.csv
2025-05-27 11:23:37,882 - INFO -    - Saved 104 records to cleaned_bookings.csv
2025-05-27 11:23:37,883 - INFO -    - Saved 73 records to cleaned_invoices.csv
2025-05-27 11:23:37,887 - INFO -    - Saved 12 records to cleaned_products.csv
2025-05-27 11:23:37,888 - INFO -    - Saved 39 records to cleaned_corporate_emails.csv
2025-05-27 11:23:37,888 - INFO - 
[PHASE 2] DUPLICATE RESOLUTION
2025-05-27 11:23:37,888 - INFO - --------------------------------------------------
2025-05-27 11:23:37,888 - INFO - Resolving critical duplicate customers...
2025-05-27 11:23:37,888 - INFO - [SUCCESS] Applied business rules for critical duplicates:
2025-05-27 11:23:37,888 - INFO -    - Jessica Endsor: Merged business and personal emails
2025-05-27 11:23:37,889 - INFO -    - Electric Lady Land/Matt Ambler: Consolidated business contact
2025-05-27 11:23:37,889 - INFO -    - Kate - Virtue & Vice: Corrected email typos and merged
2025-05-27 11:23:37,889 - INFO - 
[PHASE 3] DATA IMPORT TO SUPABASE
2025-05-27 11:23:37,889 - INFO - --------------------------------------------------
2025-05-27 11:23:37,889 - INFO - Starting data import to Supabase...
2025-05-27 11:23:38,028 - INFO - Prepared 989 customer records for import
2025-05-27 11:23:38,253 - ERROR - Batch 1 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:23:38,422 - INFO - Inserted batch 2: 100 records into customers
2025-05-27 11:23:38,601 - INFO - Inserted batch 3: 100 records into customers
2025-05-27 11:23:39,220 - INFO - Inserted batch 4: 100 records into customers
2025-05-27 11:23:39,365 - INFO - Inserted batch 5: 100 records into customers
2025-05-27 11:23:39,524 - INFO - Inserted batch 6: 100 records into customers
2025-05-27 11:23:39,666 - INFO - Inserted batch 7: 100 records into customers
2025-05-27 11:23:39,880 - INFO - Inserted batch 8: 100 records into customers
2025-05-27 11:23:40,084 - ERROR - Batch 9 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:23:40,293 - ERROR - Batch 10 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:23:40,293 - INFO - [SUCCESS] Customers: 700 imported, 289 errors
2025-05-27 11:23:40,295 - INFO - Prepared 12 product records for import
2025-05-27 11:23:40,453 - ERROR - Batch 1 failed: {"code":"23505","details":"Key (sku)=(Cosmic SplitCake) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"products_sku_key\""}
2025-05-27 11:23:40,454 - INFO - [SUCCESS] Products: 0 imported, 12 errors
2025-05-27 11:23:40,454 - INFO - Prepared 0 contact inquiry records for import
2025-05-27 11:23:40,454 - INFO - [INFO] No contact inquiry data to import
2025-05-27 11:23:40,456 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,457 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,458 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,458 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,458 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,458 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,458 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,458 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,458 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,459 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,459 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,459 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,459 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,459 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,460 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,460 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,460 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,460 - WARNING - No customer found for booking with email: None
2025-05-27 11:23:40,461 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,461 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,462 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,462 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,464 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,464 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,464 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,464 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,464 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,475 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,476 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,478 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,479 - WARNING - No customer found for booking with email: <EMAIL>
2025-05-27 11:23:40,483 - INFO - Prepared 73 booking records for import
2025-05-27 11:23:40,597 - ERROR - Batch 1 failed: {"code":"23502","details":"Failing row contains (b533d225-19c9-40e6-96f1-b474c6b5cee8, edbca561-a438-4c80-96e3-4d21759ce416, null, null, null, confirmed, null, null, 2025-05-27 01:23:38.064412+00, 2025-05-27 01:23:38.064412+00, f, null, null, null, null, wix_migration, null, null, 0, 0, 1, null, null, OSS202505279270, t, null, null, 10067.0, null, paid, null, Australia, null, Jess, 1, null, null, null, null).","hint":null,"message":"null value in column \"start_time\" of relation \"bookings\" violates not-null constraint"}
2025-05-27 11:23:40,598 - INFO - [SUCCESS] Bookings: 0 imported, 73 errors
2025-05-27 11:23:40,602 - INFO - Prepared 0 invoice records for import
2025-05-27 11:23:40,605 - INFO - Prepared 39 email campaign records for import
2025-05-27 11:23:40,718 - ERROR - Batch 1 failed: {"code":"23503","details":"Key (customer_id)=(2b4c9a20-567b-4ba6-8cb2-698c0b5bfbfa) is not present in table \"customers\".","hint":null,"message":"insert or update on table \"email_campaigns\" violates foreign key constraint \"email_campaigns_customer_id_fkey\""}
2025-05-27 11:23:40,719 - INFO - [SUCCESS] Email Campaigns: 0 imported, 39 errors
2025-05-27 11:23:40,719 - INFO - 
[PHASE 4] VALIDATION AND VERIFICATION
2025-05-27 11:23:40,719 - INFO - --------------------------------------------------
2025-05-27 11:23:40,719 - INFO - Executing validation checks...
2025-05-27 11:23:41,045 - INFO - Validation Results:
2025-05-27 11:23:41,045 - INFO -    [DATA] Customers imported: 1
2025-05-27 11:23:41,045 - INFO -    [DATA] Bookings imported: 1
2025-05-27 11:23:41,045 - INFO -    [DATA] Invoices imported: 1
2025-05-27 11:23:41,045 - INFO -    [DATA] Total records imported: 700
2025-05-27 11:23:41,045 - INFO -    [DATA] Total errors: 413
2025-05-27 11:23:41,046 - INFO -    [FAIL] Customer Data Accuracy: 0.2%
2025-05-27 11:23:41,046 - INFO -    [PASS] Financial Reconciliation: 100.0%
2025-05-27 11:23:41,046 - INFO -    [FAIL] Duplicate Customer Rate: 0.5%
2025-05-27 11:23:41,046 - INFO -    [PASS] Email Validity Rate: 98.0%
2025-05-27 11:23:41,046 - INFO -    [PASS] Phone Standardization: 100.0%
2025-05-27 11:23:41,046 - INFO -    [FAIL] Data Completeness: 62.9%
2025-05-27 11:23:41,046 - INFO -    [PASS] Foreign Key Integrity: 100.0%
2025-05-27 11:23:41,046 - WARNING - [FAIL] customer_data_accuracy (0.2%) below threshold (99.0%)
2025-05-27 11:23:41,046 - WARNING - [WARNING] Some success criteria not met - review required
2025-05-27 11:23:41,047 - INFO - 
[PHASE 5] FINAL REPORTING
2025-05-27 11:23:41,047 - INFO - --------------------------------------------------
2025-05-27 11:23:41,048 - INFO - [INFO] Final report saved to: migration-completion-report.md
2025-05-27 11:23:41,048 - INFO - 
[SUCCESS] MIGRATION COMPLETED SUCCESSFULLY!
2025-05-27 11:23:41,048 - INFO - ============================================================
2025-05-27 11:27:08,001 - INFO - ============================================================
2025-05-27 11:27:08,001 - INFO - OCEAN SOUL SPARKLES - DATA MIGRATION EXECUTION
2025-05-27 11:27:08,002 - INFO - ============================================================
2025-05-27 11:27:08,002 - INFO - Migration started at: 2025-05-27 11:27:08.000965
2025-05-27 11:27:08,002 - INFO - 
[PHASE 1] DATA CLEANING AND PREPARATION
2025-05-27 11:27:08,002 - INFO - --------------------------------------------------
2025-05-27 11:27:08,002 - INFO - Loading and cleaning CSV data files...
2025-05-27 11:27:08,003 - INFO - Starting comprehensive data cleaning process...
2025-05-27 11:27:08,003 - INFO - Processing contacts .Google csv...
2025-05-27 11:27:08,020 - INFO - Loaded 1022 records from contacts .Google csv
2025-05-27 11:27:08,029 - WARNING - Unable to standardize phone number: +41476488841
2025-05-27 11:27:08,029 - WARNING - Unable to standardize phone number: +93422771273
2025-05-27 11:27:08,029 - WARNING - Incomplete phone number requires manual review: 54436375
2025-05-27 11:27:08,030 - WARNING - Unable to standardize phone number: 04439595029
2025-05-27 11:27:08,030 - WARNING - Unable to standardize phone number: +447932773139
2025-05-27 11:27:08,031 - WARNING - Unable to standardize phone number: +81453196127
2025-05-27 11:27:08,031 - WARNING - Unable to standardize phone number: +919830152514
2025-05-27 11:27:08,032 - WARNING - Unable to standardize phone number: +612344953
2025-05-27 11:27:08,033 - INFO - Cleaned contacts .Google csv: 1022 records
2025-05-27 11:27:08,033 - INFO - Processing contacts Regular.csv...
2025-05-27 11:27:08,043 - INFO - Loaded 1022 records from contacts Regular.csv
2025-05-27 11:27:08,045 - INFO - Cleaned contacts Regular.csv: 1022 records
2025-05-27 11:27:08,045 - INFO - Processing Booking list-5_14_2025.csv...
2025-05-27 11:27:08,078 - INFO - Loaded 104 records from Booking list-5_14_2025.csv
2025-05-27 11:27:08,081 - INFO - Cleaning booking data...
2025-05-27 11:27:08,083 - INFO - Found 57 bookings with incomplete names
2025-05-27 11:27:08,083 - INFO - Found 17 bookings with incomplete emails
2025-05-27 11:27:08,083 - INFO - Found 23 bookings with incomplete phones
2025-05-27 11:27:08,084 - INFO - Cleaned Booking list-5_14_2025.csv: 104 records
2025-05-27 11:27:08,084 - INFO - Processing invoices.csv...
2025-05-27 11:27:08,086 - INFO - Loaded 73 records from invoices.csv
2025-05-27 11:27:08,093 - INFO - Cleaned invoices.csv: 73 records
2025-05-27 11:27:08,093 - INFO - Processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv...
2025-05-27 11:27:08,094 - ERROR - Error processing Revenue Summary Report WIX 1.7.24-14.5.25 .csv: Error tokenizing data. C error: Expected 1 fields in line 4, saw 5

2025-05-27 11:27:08,095 - INFO - Processing catalog_products.csv...
2025-05-27 11:27:08,100 - INFO - Loaded 12 records from catalog_products.csv
2025-05-27 11:27:08,103 - INFO - Cleaned catalog_products.csv: 12 records
2025-05-27 11:27:08,103 - INFO - Processing Corporate emails.csv...
2025-05-27 11:27:08,105 - INFO - Loaded 39 records from Corporate emails.csv
2025-05-27 11:27:08,107 - INFO - Cleaned Corporate emails.csv: 39 records
2025-05-27 11:27:08,107 - INFO - Resolving customer duplicates...
2025-05-27 11:27:08,119 - INFO - [SUCCESS] Data cleaning completed successfully
2025-05-27 11:27:08,119 - INFO -    - Phone numbers standardized: 911
2025-05-27 11:27:08,119 - INFO -    - Email addresses validated: 1022
2025-05-27 11:27:08,120 - INFO -    - Duplicate customers identified: 0
2025-05-27 11:27:08,120 - INFO -    - Duplicates resolved: 0
2025-05-27 11:27:08,158 - INFO -    - Saved 1022 records to cleaned_google_contacts.csv
2025-05-27 11:27:08,171 - INFO -    - Saved 1022 records to cleaned_regular_contacts.csv
2025-05-27 11:27:08,175 - INFO -    - Saved 104 records to cleaned_bookings.csv
2025-05-27 11:27:08,177 - INFO -    - Saved 73 records to cleaned_invoices.csv
2025-05-27 11:27:08,181 - INFO -    - Saved 12 records to cleaned_products.csv
2025-05-27 11:27:08,182 - INFO -    - Saved 39 records to cleaned_corporate_emails.csv
2025-05-27 11:27:08,182 - INFO - 
[PHASE 2] DUPLICATE RESOLUTION
2025-05-27 11:27:08,182 - INFO - --------------------------------------------------
2025-05-27 11:27:08,182 - INFO - Resolving critical duplicate customers...
2025-05-27 11:27:08,183 - INFO - [SUCCESS] Applied business rules for critical duplicates:
2025-05-27 11:27:08,183 - INFO -    - Jessica Endsor: Merged business and personal emails
2025-05-27 11:27:08,183 - INFO -    - Electric Lady Land/Matt Ambler: Consolidated business contact
2025-05-27 11:27:08,183 - INFO -    - Kate - Virtue & Vice: Corrected email typos and merged
2025-05-27 11:27:08,183 - INFO - 
[PHASE 3] DATA IMPORT TO SUPABASE
2025-05-27 11:27:08,183 - INFO - --------------------------------------------------
2025-05-27 11:27:08,183 - INFO - Starting data import to Supabase...
2025-05-27 11:27:08,778 - INFO - Loaded 715 existing customers for lookup
2025-05-27 11:27:08,928 - INFO - Prepared 989 customer records for import
2025-05-27 11:27:09,074 - ERROR - Batch 1 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:09,182 - ERROR - Batch 2 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:09,294 - ERROR - Batch 3 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:09,414 - ERROR - Batch 4 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:09,523 - ERROR - Batch 5 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:09,627 - ERROR - Batch 6 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:09,741 - ERROR - Batch 7 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:09,838 - ERROR - Batch 8 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:09,957 - ERROR - Batch 9 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:10,066 - ERROR - Batch 10 failed: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 11:27:10,066 - INFO - [SUCCESS] Customers: 0 imported, 989 errors
2025-05-27 11:27:10,068 - INFO - Prepared 12 product records for import
2025-05-27 11:27:10,160 - ERROR - Batch 1 failed: {"code":"23505","details":"Key (sku)=(Cosmic SplitCake) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"products_sku_key\""}
2025-05-27 11:27:10,160 - INFO - [SUCCESS] Products: 0 imported, 12 errors
2025-05-27 11:27:10,160 - INFO - Prepared 0 contact inquiry records for import
2025-05-27 11:27:10,160 - INFO - [INFO] No contact inquiry data to import
2025-05-27 11:27:10,168 - ERROR - Data import failed: cannot access local variable 'datetime' where it is not associated with a value
2025-05-27 11:27:10,168 - ERROR - [ERROR] MIGRATION FAILED: cannot access local variable 'datetime' where it is not associated with a value
2025-05-27 11:27:10,169 - ERROR - [ERROR] Error report saved to: migration-error-report.md
