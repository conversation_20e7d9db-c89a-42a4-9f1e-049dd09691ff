// Account page for customers to manage their profile and order history
import { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import Layout from '@/components/Layout';
import CustomerProfile from '@/components/CustomerProfile';
import { useAuth } from '@/contexts/AuthContext';
import { useCustomer } from '@/contexts/CustomerContext';
import CustomerForm from '@/components/CustomerForm';
import styles from '@/styles/Account.module.css';

export default function Account() {
  const router = useRouter();
  const { isAuthenticated, signOut } = useAuth();
  const { customer, loading, error, clearCustomer } = useCustomer();
  const [activeTab, setActiveTab] = useState('profile');

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      clearCustomer();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Redirect to sign in page if not authenticated
  if (!loading && !customer) {
    return (
      <Layout>
        <Head>
          <title>Account | OceanSoulSparkles</title>
          <meta name="description" content="Manage your OceanSoulSparkles account, view order history, and update your profile." />
        </Head>

        <main className={styles.main}>
          <div className={styles.notAuthenticated}>
            <h1>Account Access</h1>
            <p>Please sign in or create an account to access this page.</p>
            <div className={styles.authButtons}>
              <CustomerForm />
            </div>
          </div>
        </main>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>My Account | OceanSoulSparkles</title>
        <meta name="description" content="Manage your OceanSoulSparkles account, view order history, and update your profile." />
      </Head>

      <main className={styles.main}>
        <div className={styles.pageHeader}>
          <h1>My Account</h1>
          <button onClick={handleSignOut} className={styles.signOutButton}>
            Sign Out
          </button>
        </div>

        <div className={styles.accountContainer}>
          <aside className={styles.sidebar}>
            <nav className={styles.accountNav}>
              <button
                className={`${styles.navLink} ${activeTab === 'profile' ? styles.active : ''}`}
                onClick={() => setActiveTab('profile')}
              >
                Profile
              </button>
              <button
                className={`${styles.navLink} ${activeTab === 'orders' ? styles.active : ''}`}
                onClick={() => setActiveTab('orders')}
              >
                Order History
              </button>
              <button
                className={`${styles.navLink} ${activeTab === 'bookings' ? styles.active : ''}`}
                onClick={() => setActiveTab('bookings')}
              >
                Booking History
              </button>
              <button
                className={`${styles.navLink} ${activeTab === 'notifications' ? styles.active : ''}`}
                onClick={() => setActiveTab('notifications')}
              >
                Notification Preferences
              </button>
              <button
                className={`${styles.navLink} ${activeTab === 'security' ? styles.active : ''}`}
                onClick={() => setActiveTab('security')}
              >
                Security
              </button>
            </nav>
          </aside>

          <div className={styles.accountContent}>
            {loading ? (
              <div className={styles.loading}>Loading your account information...</div>
            ) : error ? (
              <div className={styles.error}>Error: {error.message}</div>
            ) : (
              <>
                {activeTab === 'profile' && (
                  <CustomerProfile />
                )}
                {activeTab === 'orders' && (
                  <div className={styles.ordersSection}>
                    <h2>Order History</h2>
                    <p>
                      {/* This would be populated with real order data in production */}
                      Your order history will appear here. You haven't placed any orders yet.
                    </p>
                    <Link href="/shop" className={styles.shopButton}>
                      Browse Shop
                    </Link>
                  </div>
                )}
                {activeTab === 'bookings' && (
                  <div className={styles.bookingsSection}>
                    <h2>Your Bookings</h2>
                    <p>
                      {/* This would be populated with real booking data in production */}
                      Your booking history will appear here. You don't have any past or upcoming bookings.
                    </p>
                    <Link href="/book-online" className={styles.bookButton}>
                      Book Services
                    </Link>
                  </div>
                )}
                {activeTab === 'notifications' && (
                  <div className={styles.notificationsSection}>
                    <h2>Notification Preferences</h2>
                    <div className={styles.notificationOptions}>
                      <div className={styles.notificationOption}>
                        <div className={styles.optionDetails}>
                          <h3>Transactional Emails</h3>
                          <p>Order confirmations, booking updates, and account notifications.</p>
                        </div>
                        <div className={styles.optionStatus}>
                          <span className={styles.requiredBadge}>Required</span>
                        </div>
                      </div>
                      <div className={styles.notificationOption}>
                        <div className={styles.optionDetails}>
                          <h3>Marketing Communications</h3>
                          <p>Promotions, new products, and event announcements.</p>
                        </div>
                        <div className={styles.optionStatus}>
                          <label className={styles.switch}>
                            <input
                              type="checkbox"
                              checked={customer.marketing_consent}
                              onChange={async () => {
                                try {
                                  // This would update the marketing preference in production
                                  alert(`Marketing preference would be updated to: ${!customer.marketing_consent}`);
                                } catch (error) {
                                  console.error('Error updating notification preferences:', error);
                                }
                              }}
                            />
                            <span className={styles.slider}></span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {activeTab === 'security' && (
                  <div className={styles.securitySection}>
                    <h2>Security Settings</h2>
                    <div className={styles.passwordSection}>
                      <h3>Change Password</h3>
                      <button
                        className={styles.changePasswordButton}
                        onClick={() => alert('Password change functionality would be implemented here')}
                      >
                        Update Password
                      </button>
                      <p>For security reasons, you will need to verify your identity.</p>
                    </div>
                    <div className={styles.accountActions}>
                      <button
                        className={styles.dangerButton}
                        onClick={() => alert('Account deletion functionality would be implemented here')}
                      >
                        Delete Account
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </main>
    </Layout>
  );
}
