2025-05-27 14:00:04,571 - ERROR - Failed to load customers: {"code":"42703","details":null,"hint":null,"message":"column customers.phone_primary does not exist"}
2025-05-27 14:00:04,572 - INFO - Starting enhanced contact inquiry import process...
2025-05-27 14:00:04,589 - INFO - Loaded 35 contact inquiries from CSV
2025-05-27 14:00:04,590 - INFO - Processing inquiry from: '<PERSON>'
2025-05-27 14:00:04,695 - ERROR - Failed to create customer <PERSON>: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:04,697 - ERROR - Failed to create customer for inquiry from <PERSON>less
2025-05-27 14:00:04,698 - INFO - Processing inquiry from: '<PERSON><PERSON>'
2025-05-27 14:00:04,789 - ERROR - Failed to create customer <PERSON><PERSON>: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:04,790 - ERROR - Failed to create customer for inquiry from Nikkita Wright
2025-05-27 14:00:04,791 - INFO - Processing inquiry from: 'Abi'
2025-05-27 14:00:04,867 - ERROR - Failed to create customer Abi: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:04,868 - ERROR - Failed to create customer for inquiry from Abi
2025-05-27 14:00:04,868 - INFO - Processing inquiry from: 'Keeva'
2025-05-27 14:00:04,938 - ERROR - Failed to create customer Keeva: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:04,938 - ERROR - Failed to create customer for inquiry from Keeva
2025-05-27 14:00:04,939 - INFO - Processing inquiry from: 'Lisa Zheng'
2025-05-27 14:00:05,006 - ERROR - Failed to create customer Lisa Zheng: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,007 - ERROR - Failed to create customer for inquiry from Lisa Zheng
2025-05-27 14:00:05,007 - INFO - Processing inquiry from: 'Kate'
2025-05-27 14:00:05,077 - ERROR - Failed to create customer Kate: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,078 - ERROR - Failed to create customer for inquiry from Kate
2025-05-27 14:00:05,078 - INFO - Processing inquiry from: 'Casey Vance'
2025-05-27 14:00:05,155 - ERROR - Failed to create customer Casey Vance: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,156 - ERROR - Failed to create customer for inquiry from Casey Vance
2025-05-27 14:00:05,156 - INFO - Processing inquiry from: 'Patricia Bowlby'
2025-05-27 14:00:05,237 - INFO - Created customer: Patricia Bowlby (ID: 60c4f60b-f66e-4a72-9dbc-5073d90e4e8b)
2025-05-27 14:00:05,369 - ERROR - Failed to import inquiry: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'notes' column of 'contact_inquiries' in the schema cache"}
2025-05-27 14:00:05,370 - INFO - Processing inquiry from: 'Ally Wilson'
2025-05-27 14:00:05,464 - ERROR - Failed to create customer Ally Wilson: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,464 - ERROR - Failed to create customer for inquiry from Ally Wilson
2025-05-27 14:00:05,465 - INFO - Processing inquiry from: 'Lisa Zheng'
2025-05-27 14:00:05,533 - ERROR - Failed to create customer Lisa Zheng: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,533 - ERROR - Failed to create customer for inquiry from Lisa Zheng
2025-05-27 14:00:05,534 - INFO - Processing inquiry from: 'Techa'
2025-05-27 14:00:05,607 - ERROR - Failed to create customer Techa: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,607 - ERROR - Failed to create customer for inquiry from Techa
2025-05-27 14:00:05,608 - INFO - Processing inquiry from: 'yael'
2025-05-27 14:00:05,682 - ERROR - Failed to create customer yael: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,683 - ERROR - Failed to create customer for inquiry from yael
2025-05-27 14:00:05,683 - INFO - Processing inquiry from: 'Rebecca'
2025-05-27 14:00:05,767 - ERROR - Failed to create customer Rebecca: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,767 - ERROR - Failed to create customer for inquiry from Rebecca
2025-05-27 14:00:05,768 - INFO - Processing inquiry from: 'Georgina'
2025-05-27 14:00:05,839 - ERROR - Failed to create customer Georgina: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,839 - ERROR - Failed to create customer for inquiry from Georgina
2025-05-27 14:00:05,840 - INFO - Processing inquiry from: 'Kate - Virtue & Vice'
2025-05-27 14:00:05,909 - ERROR - Failed to create customer Kate - Virtue & Vice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,909 - ERROR - Failed to create customer for inquiry from Kate - Virtue & Vice
2025-05-27 14:00:05,910 - INFO - Processing inquiry from: 'Jules Konda'
2025-05-27 14:00:05,982 - ERROR - Failed to create customer Jules Konda: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:05,982 - ERROR - Failed to create customer for inquiry from Jules Konda
2025-05-27 14:00:05,982 - INFO - Processing inquiry from: 'Pat'
2025-05-27 14:00:06,058 - ERROR - Failed to create customer Pat: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,058 - ERROR - Failed to create customer for inquiry from Pat
2025-05-27 14:00:06,059 - INFO - Processing inquiry from: 'Christine'
2025-05-27 14:00:06,129 - ERROR - Failed to create customer Christine: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,130 - ERROR - Failed to create customer for inquiry from Christine
2025-05-27 14:00:06,131 - INFO - Processing inquiry from: 'Amanda Gill'
2025-05-27 14:00:06,208 - ERROR - Failed to create customer Amanda Gill: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,208 - ERROR - Failed to create customer for inquiry from Amanda Gill
2025-05-27 14:00:06,209 - INFO - Processing inquiry from: 'Bec'
2025-05-27 14:00:06,288 - ERROR - Failed to create customer Bec: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,289 - ERROR - Failed to create customer for inquiry from Bec
2025-05-27 14:00:06,289 - INFO - Processing inquiry from: 'Jess'
2025-05-27 14:00:06,363 - ERROR - Failed to create customer Jess: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,363 - ERROR - Failed to create customer for inquiry from Jess
2025-05-27 14:00:06,364 - INFO - Processing inquiry from: 'Kathy'
2025-05-27 14:00:06,431 - ERROR - Failed to create customer Kathy: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,432 - ERROR - Failed to create customer for inquiry from Kathy
2025-05-27 14:00:06,432 - INFO - Processing inquiry from: 'Cyrene Ortega'
2025-05-27 14:00:06,501 - ERROR - Failed to create customer Cyrene Ortega: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,502 - ERROR - Failed to create customer for inquiry from Cyrene Ortega
2025-05-27 14:00:06,502 - INFO - Processing inquiry from: 'Veni May'
2025-05-27 14:00:06,566 - ERROR - Failed to create customer Veni May: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,566 - ERROR - Failed to create customer for inquiry from Veni May
2025-05-27 14:00:06,567 - INFO - Processing inquiry from: 'Kylie Madigan'
2025-05-27 14:00:06,647 - ERROR - Failed to create customer Kylie Madigan: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,650 - ERROR - Failed to create customer for inquiry from Kylie Madigan
2025-05-27 14:00:06,651 - INFO - Processing inquiry from: 'Charlee'
2025-05-27 14:00:06,729 - ERROR - Failed to create customer Charlee: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,730 - ERROR - Failed to create customer for inquiry from Charlee
2025-05-27 14:00:06,731 - INFO - Processing inquiry from: 'Kathryn  Bess'
2025-05-27 14:00:06,820 - INFO - Created customer: Kathryn  Bess (ID: a12f40c6-7bc5-4c77-a1bb-9d12f8fff481)
2025-05-27 14:00:06,907 - ERROR - Failed to import inquiry: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'notes' column of 'contact_inquiries' in the schema cache"}
2025-05-27 14:00:06,908 - INFO - Processing inquiry from: 'Veni May'
2025-05-27 14:00:06,986 - ERROR - Failed to create customer Veni May: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:06,987 - ERROR - Failed to create customer for inquiry from Veni May
2025-05-27 14:00:06,987 - INFO - Processing inquiry from: 'Tania Ferraro'
2025-05-27 14:00:07,057 - ERROR - Failed to create customer Tania Ferraro: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:07,058 - ERROR - Failed to create customer for inquiry from Tania Ferraro
2025-05-27 14:00:07,058 - INFO - Processing inquiry from: 'Cam'
2025-05-27 14:00:07,127 - ERROR - Failed to create customer Cam: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:07,128 - ERROR - Failed to create customer for inquiry from Cam
2025-05-27 14:00:07,130 - INFO - Processing inquiry from: 'Zak Gatara'
2025-05-27 14:00:07,204 - INFO - Created customer: Zak Gatara (ID: bd4ef205-ccae-4388-b0e1-1c9fab96fb8f)
2025-05-27 14:00:07,272 - ERROR - Failed to import inquiry: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'notes' column of 'contact_inquiries' in the schema cache"}
2025-05-27 14:00:07,273 - INFO - Processing inquiry from: 'Steve & Adi Smith'
2025-05-27 14:00:07,348 - ERROR - Failed to create customer Steve & Adi Smith: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:07,348 - ERROR - Failed to create customer for inquiry from Steve & Adi Smith
2025-05-27 14:00:07,349 - INFO - Processing inquiry from: 'Jessa'
2025-05-27 14:00:07,428 - INFO - Created customer: Jessa (ID: 875e6b17-9661-4f93-9a7a-af072e74bf63)
2025-05-27 14:00:07,494 - ERROR - Failed to import inquiry: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'notes' column of 'contact_inquiries' in the schema cache"}
2025-05-27 14:00:07,495 - INFO - Processing inquiry from: 'Jessica'
2025-05-27 14:00:07,568 - ERROR - Failed to create customer Jessica: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:07,568 - ERROR - Failed to create customer for inquiry from Jessica
2025-05-27 14:00:07,569 - INFO - Processing inquiry from: 'Emma Reid'
2025-05-27 14:00:07,646 - ERROR - Failed to create customer Emma Reid: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'phone_primary' column of 'customers' in the schema cache"}
2025-05-27 14:00:07,646 - ERROR - Failed to create customer for inquiry from Emma Reid
2025-05-27 14:00:07,647 - INFO - Generated contact inquiry import report
2025-05-27 14:00:07,648 - INFO - Contact inquiry import completed successfully!
2025-05-27 14:02:58,441 - INFO - Loaded 760 customers for matching
2025-05-27 14:02:58,442 - INFO - Starting enhanced contact inquiry import process...
2025-05-27 14:02:58,449 - INFO - Loaded 35 contact inquiries from CSV
2025-05-27 14:02:58,450 - WARNING - Expected ~134 inquiries but only loaded 35. Checking file...
2025-05-27 14:02:58,451 - INFO - Processing inquiry from: 'Veronica Quinless'
2025-05-27 14:02:58,541 - INFO - Created customer: Veronica Quinless (ID: 61a60fe8-84a5-415c-a76c-045c00b922a4)
2025-05-27 14:02:58,621 - INFO - Imported inquiry from Veronica Quinless (match: new_customer)
2025-05-27 14:02:58,621 - INFO - Processing inquiry from: 'Nikkita Wright'
2025-05-27 14:02:58,626 - INFO - Processing inquiry from: 'Abi'
2025-05-27 14:02:58,709 - INFO - Imported inquiry from Abi (match: exact_match)
2025-05-27 14:02:58,709 - INFO - Processing inquiry from: 'Keeva'
2025-05-27 14:02:58,787 - INFO - Created customer: Keeva (ID: 21fcc765-cf67-4c07-a2f0-661ef1b37190)
2025-05-27 14:02:58,864 - INFO - Imported inquiry from Keeva (match: new_customer)
2025-05-27 14:02:58,864 - INFO - Processing inquiry from: 'Lisa Zheng'
2025-05-27 14:02:58,940 - INFO - Created customer: Lisa Zheng (ID: ab513a16-543b-4533-aca7-a4a1b5c705f5)
2025-05-27 14:02:59,037 - INFO - Imported inquiry from Lisa Zheng (match: new_customer)
2025-05-27 14:02:59,038 - INFO - Processing inquiry from: 'Kate'
2025-05-27 14:02:59,118 - INFO - Imported inquiry from Kate (match: exact_match)
2025-05-27 14:02:59,119 - INFO - Processing inquiry from: 'Casey Vance'
2025-05-27 14:02:59,204 - INFO - Imported inquiry from Casey Vance (match: exact_match)
2025-05-27 14:02:59,205 - INFO - Processing inquiry from: 'Patricia Bowlby'
2025-05-27 14:02:59,280 - INFO - Imported inquiry from Patricia Bowlby (match: email_exact)
2025-05-27 14:02:59,280 - INFO - Processing inquiry from: 'Ally Wilson'
2025-05-27 14:02:59,363 - INFO - Imported inquiry from Ally Wilson (match: exact_match)
2025-05-27 14:02:59,363 - INFO - Processing inquiry from: 'Lisa Zheng'
2025-05-27 14:02:59,478 - ERROR - Failed to create customer Lisa Zheng: {"code":"23505","details":"Key (email)=(<EMAIL>) already exists.","hint":null,"message":"duplicate key value violates unique constraint \"customers_email_key\""}
2025-05-27 14:02:59,478 - ERROR - Failed to create customer for inquiry from Lisa Zheng
2025-05-27 14:02:59,479 - INFO - Processing inquiry from: 'Techa'
2025-05-27 14:02:59,557 - INFO - Imported inquiry from Techa (match: email_exact)
2025-05-27 14:02:59,558 - INFO - Processing inquiry from: 'yael'
2025-05-27 14:02:59,642 - INFO - Created customer: yael (ID: 06288018-22f0-4ac7-91c2-93acd17338e2)
2025-05-27 14:02:59,720 - INFO - Imported inquiry from yael (match: new_customer)
2025-05-27 14:02:59,720 - INFO - Processing inquiry from: 'Rebecca'
2025-05-27 14:02:59,794 - INFO - Imported inquiry from Rebecca (match: exact_match)
2025-05-27 14:02:59,794 - INFO - Processing inquiry from: 'Georgina'
2025-05-27 14:02:59,872 - INFO - Created customer: Georgina (ID: aea2a8e7-2351-4154-bef2-13cd8239d599)
2025-05-27 14:02:59,943 - INFO - Imported inquiry from Georgina (match: new_customer)
2025-05-27 14:02:59,943 - INFO - Processing inquiry from: 'Kate - Virtue & Vice'
2025-05-27 14:03:00,026 - INFO - Imported inquiry from Kate - Virtue & Vice (match: phone_exact)
2025-05-27 14:03:00,026 - INFO - Processing inquiry from: 'Jules Konda'
2025-05-27 14:03:00,106 - INFO - Created customer: Jules Konda (ID: f501e991-ecdc-4d9f-b241-f6c70ffecfb8)
2025-05-27 14:03:00,177 - INFO - Imported inquiry from Jules Konda (match: new_customer)
2025-05-27 14:03:00,178 - INFO - Processing inquiry from: 'Pat'
2025-05-27 14:03:00,260 - INFO - Imported inquiry from Pat (match: exact_match)
2025-05-27 14:03:00,261 - INFO - Processing inquiry from: 'Christine'
2025-05-27 14:03:00,333 - INFO - Imported inquiry from Christine (match: exact_match)
2025-05-27 14:03:00,334 - INFO - Processing inquiry from: 'Amanda Gill'
2025-05-27 14:03:00,406 - INFO - Imported inquiry from Amanda Gill (match: email_exact)
2025-05-27 14:03:00,406 - INFO - Processing inquiry from: 'Bec'
2025-05-27 14:03:00,482 - INFO - Imported inquiry from Bec (match: exact_match)
2025-05-27 14:03:00,483 - INFO - Processing inquiry from: 'Jess'
2025-05-27 14:03:00,556 - INFO - Imported inquiry from Jess (match: exact_match)
2025-05-27 14:03:00,557 - INFO - Processing inquiry from: 'Kathy'
2025-05-27 14:03:00,661 - INFO - Imported inquiry from Kathy (match: exact_match)
2025-05-27 14:03:00,662 - INFO - Processing inquiry from: 'Cyrene Ortega'
2025-05-27 14:03:00,940 - INFO - Created customer: Cyrene Ortega (ID: caf73572-4ab6-4963-ae23-e5a1ea984977)
2025-05-27 14:03:01,019 - INFO - Imported inquiry from Cyrene Ortega (match: new_customer)
2025-05-27 14:03:01,020 - INFO - Processing inquiry from: 'Veni May'
2025-05-27 14:03:01,098 - INFO - Imported inquiry from Veni May (match: exact_match)
2025-05-27 14:03:01,098 - INFO - Processing inquiry from: 'Kylie Madigan'
2025-05-27 14:03:01,170 - INFO - Created customer: Kylie Madigan (ID: d8796bf1-8685-4710-99b4-ee5f352fc957)
2025-05-27 14:03:01,237 - INFO - Imported inquiry from Kylie Madigan (match: new_customer)
2025-05-27 14:03:01,238 - INFO - Processing inquiry from: 'Charlee'
2025-05-27 14:03:01,315 - INFO - Created customer: Charlee (ID: 55283474-c897-40e1-abb5-ca25862ed0a8)
2025-05-27 14:03:01,383 - INFO - Imported inquiry from Charlee (match: new_customer)
2025-05-27 14:03:01,384 - INFO - Processing inquiry from: 'Kathryn  Bess'
2025-05-27 14:03:01,456 - INFO - Imported inquiry from Kathryn  Bess (match: email_exact)
2025-05-27 14:03:01,456 - INFO - Processing inquiry from: 'Veni May'
2025-05-27 14:03:01,529 - INFO - Imported inquiry from Veni May (match: exact_match)
2025-05-27 14:03:01,530 - INFO - Processing inquiry from: 'Tania Ferraro'
2025-05-27 14:03:01,607 - INFO - Created customer: Tania Ferraro (ID: 1cf4ef51-b460-43ea-a122-3ac406421dbc)
2025-05-27 14:03:01,681 - INFO - Imported inquiry from Tania Ferraro (match: new_customer)
2025-05-27 14:03:01,682 - INFO - Processing inquiry from: 'Cam'
2025-05-27 14:03:01,756 - INFO - Imported inquiry from Cam (match: exact_match)
2025-05-27 14:03:01,756 - INFO - Processing inquiry from: 'Zak Gatara'
2025-05-27 14:03:01,842 - INFO - Imported inquiry from Zak Gatara (match: email_exact)
2025-05-27 14:03:01,842 - INFO - Processing inquiry from: 'Steve & Adi Smith'
2025-05-27 14:03:01,936 - INFO - Created customer: Steve & Adi Smith (ID: 6b73e306-4eb4-4505-b8aa-f36536f1d214)
2025-05-27 14:03:02,005 - INFO - Imported inquiry from Steve & Adi Smith (match: new_customer)
2025-05-27 14:03:02,006 - INFO - Processing inquiry from: 'Jessa'
2025-05-27 14:03:02,089 - INFO - Imported inquiry from Jessa (match: email_exact)
2025-05-27 14:03:02,090 - INFO - Processing inquiry from: 'Jessica'
2025-05-27 14:03:02,172 - INFO - Imported inquiry from Jessica (match: exact_match)
2025-05-27 14:03:02,173 - INFO - Processing inquiry from: 'Emma Reid'
2025-05-27 14:03:02,243 - INFO - Imported inquiry from Emma Reid (match: exact_match)
2025-05-27 14:03:02,244 - INFO - Saved 1 cases for manual review
2025-05-27 14:03:02,245 - INFO - Generated contact inquiry import report
2025-05-27 14:03:02,246 - INFO - Contact inquiry import completed successfully!
2025-05-27 14:04:02,971 - INFO - Loaded 771 customers for matching
2025-05-27 14:04:02,972 - INFO - Starting enhanced contact inquiry import process...
2025-05-27 14:04:02,977 - INFO - Loaded 35 contact inquiries from CSV
2025-05-27 14:04:02,978 - WARNING - Expected ~134 inquiries but only loaded 35. Checking file...
2025-05-27 14:04:02,980 - INFO - Processing inquiry from: 'Veronica Quinless'
2025-05-27 14:04:03,064 - INFO - Imported inquiry from Veronica Quinless (match: email_exact)
2025-05-27 14:04:03,065 - INFO - Processing inquiry from: 'Nikkita Wright'
2025-05-27 14:04:03,069 - INFO - Processing inquiry from: 'Abi'
2025-05-27 14:04:03,180 - INFO - Imported inquiry from Abi (match: exact_match)
2025-05-27 14:04:03,180 - INFO - Processing inquiry from: 'Keeva'
2025-05-27 14:04:03,264 - INFO - Imported inquiry from Keeva (match: email_exact)
2025-05-27 14:04:03,264 - INFO - Processing inquiry from: 'Lisa Zheng'
2025-05-27 14:04:03,346 - INFO - Imported inquiry from Lisa Zheng (match: email_exact)
2025-05-27 14:04:03,347 - INFO - Processing inquiry from: 'Kate'
2025-05-27 14:04:03,423 - INFO - Imported inquiry from Kate (match: exact_match)
2025-05-27 14:04:03,424 - INFO - Processing inquiry from: 'Casey Vance'
2025-05-27 14:04:03,503 - INFO - Imported inquiry from Casey Vance (match: exact_match)
2025-05-27 14:04:03,504 - INFO - Processing inquiry from: 'Patricia Bowlby'
2025-05-27 14:04:03,606 - INFO - Imported inquiry from Patricia Bowlby (match: email_exact)
2025-05-27 14:04:03,607 - INFO - Processing inquiry from: 'Ally Wilson'
2025-05-27 14:04:03,686 - INFO - Imported inquiry from Ally Wilson (match: exact_match)
2025-05-27 14:04:03,686 - INFO - Processing inquiry from: 'Lisa Zheng'
2025-05-27 14:04:03,793 - INFO - Imported inquiry from Lisa Zheng (match: email_exact)
2025-05-27 14:04:03,793 - INFO - Processing inquiry from: 'Techa'
2025-05-27 14:04:03,875 - INFO - Imported inquiry from Techa (match: email_exact)
2025-05-27 14:04:03,876 - INFO - Processing inquiry from: 'yael'
2025-05-27 14:04:03,957 - INFO - Imported inquiry from yael (match: email_exact)
2025-05-27 14:04:03,958 - INFO - Processing inquiry from: 'Rebecca'
2025-05-27 14:04:04,032 - INFO - Imported inquiry from Rebecca (match: exact_match)
2025-05-27 14:04:04,033 - INFO - Processing inquiry from: 'Georgina'
2025-05-27 14:04:04,107 - INFO - Imported inquiry from Georgina (match: email_exact)
2025-05-27 14:04:04,108 - INFO - Processing inquiry from: 'Kate - Virtue & Vice'
2025-05-27 14:04:04,196 - INFO - Imported inquiry from Kate - Virtue & Vice (match: phone_exact)
2025-05-27 14:04:04,196 - INFO - Processing inquiry from: 'Jules Konda'
2025-05-27 14:04:04,273 - INFO - Imported inquiry from Jules Konda (match: email_exact)
2025-05-27 14:04:04,273 - INFO - Processing inquiry from: 'Pat'
2025-05-27 14:04:04,371 - INFO - Imported inquiry from Pat (match: exact_match)
2025-05-27 14:04:04,372 - INFO - Processing inquiry from: 'Christine'
2025-05-27 14:04:04,447 - INFO - Imported inquiry from Christine (match: exact_match)
2025-05-27 14:04:04,447 - INFO - Processing inquiry from: 'Amanda Gill'
2025-05-27 14:04:04,539 - INFO - Imported inquiry from Amanda Gill (match: email_exact)
2025-05-27 14:04:04,539 - INFO - Processing inquiry from: 'Bec'
2025-05-27 14:04:04,623 - INFO - Imported inquiry from Bec (match: exact_match)
2025-05-27 14:04:04,623 - INFO - Processing inquiry from: 'Jess'
2025-05-27 14:04:04,701 - INFO - Imported inquiry from Jess (match: exact_match)
2025-05-27 14:04:04,701 - INFO - Processing inquiry from: 'Kathy'
2025-05-27 14:04:04,791 - INFO - Imported inquiry from Kathy (match: exact_match)
2025-05-27 14:04:04,792 - INFO - Processing inquiry from: 'Cyrene Ortega'
2025-05-27 14:04:04,864 - INFO - Imported inquiry from Cyrene Ortega (match: email_exact)
2025-05-27 14:04:04,865 - INFO - Processing inquiry from: 'Veni May'
2025-05-27 14:04:04,961 - INFO - Imported inquiry from Veni May (match: exact_match)
2025-05-27 14:04:04,962 - INFO - Processing inquiry from: 'Kylie Madigan'
2025-05-27 14:04:05,038 - INFO - Imported inquiry from Kylie Madigan (match: email_exact)
2025-05-27 14:04:05,038 - INFO - Processing inquiry from: 'Charlee'
2025-05-27 14:04:05,109 - INFO - Imported inquiry from Charlee (match: email_exact)
2025-05-27 14:04:05,110 - INFO - Processing inquiry from: 'Kathryn  Bess'
2025-05-27 14:04:05,187 - INFO - Imported inquiry from Kathryn  Bess (match: email_exact)
2025-05-27 14:04:05,187 - INFO - Processing inquiry from: 'Veni May'
2025-05-27 14:04:05,266 - INFO - Imported inquiry from Veni May (match: exact_match)
2025-05-27 14:04:05,267 - INFO - Processing inquiry from: 'Tania Ferraro'
2025-05-27 14:04:05,339 - INFO - Imported inquiry from Tania Ferraro (match: email_exact)
2025-05-27 14:04:05,339 - INFO - Processing inquiry from: 'Cam'
2025-05-27 14:04:05,410 - INFO - Imported inquiry from Cam (match: exact_match)
2025-05-27 14:04:05,410 - INFO - Processing inquiry from: 'Zak Gatara'
2025-05-27 14:04:05,497 - INFO - Imported inquiry from Zak Gatara (match: email_exact)
2025-05-27 14:04:05,497 - INFO - Processing inquiry from: 'Steve & Adi Smith'
2025-05-27 14:04:05,583 - INFO - Imported inquiry from Steve & Adi Smith (match: email_exact)
2025-05-27 14:04:05,584 - INFO - Processing inquiry from: 'Jessa'
2025-05-27 14:04:05,674 - INFO - Imported inquiry from Jessa (match: email_exact)
2025-05-27 14:04:05,674 - INFO - Processing inquiry from: 'Jessica'
2025-05-27 14:04:05,747 - INFO - Imported inquiry from Jessica (match: exact_match)
2025-05-27 14:04:05,747 - INFO - Processing inquiry from: 'Emma Reid'
2025-05-27 14:04:05,827 - INFO - Imported inquiry from Emma Reid (match: exact_match)
2025-05-27 14:04:05,828 - INFO - Saved 1 cases for manual review
2025-05-27 14:04:05,829 - INFO - Generated contact inquiry import report
2025-05-27 14:04:05,830 - INFO - Contact inquiry import completed successfully!
