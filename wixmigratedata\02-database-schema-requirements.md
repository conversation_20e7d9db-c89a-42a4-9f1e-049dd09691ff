# Database Schema Requirements for Ocean Soul Sparkles

## Overview

This document outlines the database schema requirements for migrating Ocean Soul Sparkles data from Wix to the new website platform. The schema is designed to support the business's face painting, body art, and event services.

## Core Tables

### 1. Customers Table
```sql
customers (
  id: UUID PRIMARY KEY,
  first_name: <PERSON><PERSON><PERSON><PERSON>(100),
  last_name: <PERSON><PERSON><PERSON><PERSON>(100),
  email: VARCHAR(255) UNIQUE,
  phone_primary: VARCHAR(20),
  phone_secondary: VARCHAR(20),
  date_of_birth: DATE,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP,
  email_subscription_status: ENUM('subscribed', 'unsubscribed', 'never_subscribed'),
  sms_subscription_status: ENUM('subscribed', 'unsubscribed', 'never_subscribed'),
  square_customer_id: VARCHAR(100),
  total_spend: DECIMAL(10,2),
  transaction_count: INTEGER,
  first_visit: DATE,
  last_visit: DATE,
  notes: TEXT,
  source: VARCHA<PERSON>(100),
  language: VARCHAR(10)
)
```

### 2. Customer Addresses Table
```sql
customer_addresses (
  id: UUID PRIMARY KEY,
  customer_id: UUID REFERENCES customers(id),
  address_type: ENUM('billing', 'shipping', 'work', 'home'),
  street_line_1: VARCHAR(255),
  street_line_2: VARCHAR(255),
  city: VARCHAR(100),
  state_region: VARCHAR(100),
  postal_code: VARCHAR(20),
  country: VARCHAR(100),
  is_primary: BOOLEAN DEFAULT false,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### 3. Companies Table
```sql
companies (
  id: UUID PRIMARY KEY,
  name: VARCHAR(255),
  abn: VARCHAR(20),
  industry: VARCHAR(100),
  website: VARCHAR(255),
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### 4. Customer Companies Table (Many-to-Many)
```sql
customer_companies (
  id: UUID PRIMARY KEY,
  customer_id: UUID REFERENCES customers(id),
  company_id: UUID REFERENCES companies(id),
  position: VARCHAR(100),
  is_primary: BOOLEAN DEFAULT false,
  created_at: TIMESTAMP
)
```

### 5. Services Table
```sql
services (
  id: UUID PRIMARY KEY,
  name: VARCHAR(255),
  description: TEXT,
  category: ENUM('face_painting', 'body_art', 'hair_braiding', 'glitter_bar', 'uv_painting', 'airbrush'),
  base_price: DECIMAL(10,2),
  duration_minutes: INTEGER,
  max_participants: INTEGER,
  is_active: BOOLEAN DEFAULT true,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### 6. Service Pricing Table
```sql
service_pricing (
  id: UUID PRIMARY KEY,
  service_id: UUID REFERENCES services(id),
  name: VARCHAR(255), -- e.g., "1 2 Hours", "Max 18 Kids"
  price: DECIMAL(10,2),
  duration_hours: DECIMAL(3,1),
  max_participants: INTEGER,
  description: TEXT,
  is_active: BOOLEAN DEFAULT true,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### 7. Bookings Table
```sql
bookings (
  id: UUID PRIMARY KEY,
  customer_id: UUID REFERENCES customers(id),
  order_number: VARCHAR(50) UNIQUE,
  booking_date: DATE,
  start_time: TIMESTAMP,
  end_time: TIMESTAMP,
  status: ENUM('confirmed', 'pending_approval', 'canceled', 'declined', 'completed', 'incomplete'),
  total_amount: DECIMAL(10,2),
  payment_status: ENUM('paid', 'not_paid', 'partial', 'refunded'),
  location_address: TEXT,
  special_instructions: TEXT,
  staff_member: VARCHAR(100),
  group_size: INTEGER,
  event_name: VARCHAR(255),
  event_type: VARCHAR(100),
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### 8. Booking Services Table (Many-to-Many)
```sql
booking_services (
  id: UUID PRIMARY KEY,
  booking_id: UUID REFERENCES bookings(id),
  service_pricing_id: UUID REFERENCES service_pricing(id),
  quantity: INTEGER DEFAULT 1,
  unit_price: DECIMAL(10,2),
  total_price: DECIMAL(10,2),
  created_at: TIMESTAMP
)
```

### 9. Contact Inquiries Table
```sql
contact_inquiries (
  id: UUID PRIMARY KEY,
  customer_id: UUID REFERENCES customers(id),
  subject: VARCHAR(255),
  message: TEXT,
  inquiry_type: ENUM('booking_request', 'general_inquiry', 'complaint', 'compliment'),
  status: ENUM('new', 'in_progress', 'resolved', 'closed'),
  submitted_at: TIMESTAMP,
  resolved_at: TIMESTAMP,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### 10. Products Table
```sql
products (
  id: UUID PRIMARY KEY,
  handle_id: VARCHAR(100),
  name: VARCHAR(255),
  description: TEXT,
  sku: VARCHAR(100),
  price: DECIMAL(10,2),
  cost: DECIMAL(10,2),
  weight: DECIMAL(5,2),
  inventory_quantity: INTEGER,
  is_visible: BOOLEAN DEFAULT true,
  ribbon: VARCHAR(50),
  collection: VARCHAR(100),
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### 11. Product Images Table
```sql
product_images (
  id: UUID PRIMARY KEY,
  product_id: UUID REFERENCES products(id),
  image_url: VARCHAR(500),
  alt_text: VARCHAR(255),
  sort_order: INTEGER,
  is_primary: BOOLEAN DEFAULT false,
  created_at: TIMESTAMP
)
```

### 12. Invoices Table
```sql
invoices (
  id: UUID PRIMARY KEY,
  customer_id: UUID REFERENCES customers(id),
  booking_id: UUID REFERENCES bookings(id),
  invoice_number: VARCHAR(50) UNIQUE,
  order_number: VARCHAR(50),
  status: ENUM('draft', 'sent', 'paid', 'overdue', 'void', 'refunded'),
  issue_date: DATE,
  due_date: DATE,
  currency: VARCHAR(3) DEFAULT 'AUD',
  subtotal: DECIMAL(10,2),
  discount_amount: DECIMAL(10,2),
  tax_amount: DECIMAL(10,2),
  total_amount: DECIMAL(10,2),
  payment_date: DATE,
  notes: TEXT,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

## Supporting Tables

### 13. Email Campaigns Table
```sql
email_campaigns (
  id: UUID PRIMARY KEY,
  customer_id: UUID REFERENCES customers(id),
  email: VARCHAR(255),
  campaign_name: VARCHAR(255),
  sent_at: TIMESTAMP,
  status: ENUM('sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained'),
  bounce_type: ENUM('hard_bounce', 'soft_bounce'),
  created_at: TIMESTAMP
)
```

### 14. Staff Members Table
```sql
staff_members (
  id: UUID PRIMARY KEY,
  name: VARCHAR(255),
  email: VARCHAR(255),
  phone: VARCHAR(20),
  specialties: TEXT[], -- Array of service specialties
  is_active: BOOLEAN DEFAULT true,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### 15. Booking Form Responses Table
```sql
booking_form_responses (
  id: UUID PRIMARY KEY,
  booking_id: UUID REFERENCES bookings(id),
  field_name: VARCHAR(255),
  field_value: TEXT,
  created_at: TIMESTAMP
)
```

## Indexes and Constraints

### Primary Indexes
- Customer email (unique)
- Booking order_number (unique)
- Invoice invoice_number (unique)
- Customer phone numbers
- Booking dates and times
- Invoice dates and status

### Foreign Key Constraints
- All referenced IDs must exist in parent tables
- Cascade deletes where appropriate
- Restrict deletes for critical business data

## Data Migration Considerations

1. **UUID Generation**: All new records will use UUIDs for better scalability
2. **Timestamp Handling**: Convert all dates to UTC timestamps
3. **Phone Number Standardization**: Normalize to Australian format
4. **Email Validation**: Ensure all emails are valid and lowercase
5. **Duplicate Handling**: Merge duplicate customers based on email/phone
6. **Historical Data**: Preserve all historical booking and payment data
7. **Status Mapping**: Map Wix statuses to new standardized statuses

## Performance Considerations

1. **Partitioning**: Consider partitioning large tables by date
2. **Archiving**: Archive old completed bookings and invoices
3. **Caching**: Implement caching for frequently accessed customer data
4. **Search Optimization**: Full-text search on customer names and addresses

---

*Document Version: 1.0*
*Last Updated: [Current Date]*
*Status: Draft - Pending Review*
