#!/usr/bin/env python3

"""
Enhanced Invoice Import Script for Ocean Soul Sparkles
Phase 2A: Import 73 invoice records with advanced fuzzy matching

This script implements enhanced customer name matching algorithms to resolve
the customer linkage issues that prevented invoice import in Phase 1.
"""

import pandas as pd
import requests
import json
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import re
from difflib import SequenceMatcher
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced-invoice-import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedInvoiceImporter:
    def __init__(self):
        self.supabase_url = os.getenv('NEXT_PUBLIC_SUPABASE_URL', '').strip()
        self.supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY', '').strip()

        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Missing Supabase environment variables")

        self.headers = {
            'apikey': self.supabase_key.strip(),
            'Authorization': f'Bearer {self.supabase_key.strip()}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }

        self.customers = []
        self.customer_lookup = {}
        self.match_results = {
            'exact_matches': 0,
            'fuzzy_matches': 0,
            'manual_review': 0,
            'no_match': 0
        }

    def load_customers(self):
        """Load all customers from database for matching."""
        try:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/customers?select=id,name,email",
                headers=self.headers
            )

            if response.status_code == 200:
                self.customers = response.json()
                logger.info(f"Loaded {len(self.customers)} customers for matching")

                # Create lookup dictionaries
                for customer in self.customers:
                    # Email lookup
                    if customer.get('email'):
                        self.customer_lookup[customer['email'].lower()] = customer['id']

                return True
            else:
                logger.error(f"Failed to load customers: {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error loading customers: {str(e)}")
            return False

    def normalize_name(self, name: str) -> str:
        """Normalize name for better matching."""
        if not name:
            return ""

        # Remove quotes and extra whitespace
        name = re.sub(r'^"="|"$', '', name)
        name = re.sub(r'\s+', ' ', name.strip())

        # Handle common variations
        name = name.replace(' ATF ', ' ')  # Remove ATF (As Trustee For)
        name = name.replace('Pty Ltd', '').replace('PTY LTD', '')
        name = name.replace(' Ltd', '').replace(' LTD', '')

        return name

    def calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings."""
        return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()

    def find_customer_match(self, invoice_name: str) -> Tuple[Optional[str], str, float]:
        """
        Find customer match using enhanced fuzzy matching.
        Returns: (customer_id, match_type, confidence)
        """
        if not invoice_name:
            return None, 'no_name', 0.0

        normalized_invoice = self.normalize_name(invoice_name)

        # Strategy 1: Check if it's an email address
        if '@' in invoice_name:
            email_key = invoice_name.lower()
            if email_key in self.customer_lookup:
                return self.customer_lookup[email_key], 'email_exact', 1.0

        # Strategy 2: Exact name match
        for customer in self.customers:
            if customer['name'].lower() == normalized_invoice.lower():
                return customer['id'], 'exact_match', 1.0

        # Strategy 3: Fuzzy matching with different thresholds
        best_match = None
        best_score = 0.0
        best_customer_id = None

        for customer in self.customers:
            customer_name = self.normalize_name(customer['name'])

            # Calculate similarity
            similarity = self.calculate_similarity(normalized_invoice, customer_name)

            # Check for partial matches (company names, etc.)
            if self.is_company_name(normalized_invoice) and self.is_company_name(customer_name):
                # For company names, use higher threshold
                if similarity > 0.8 and similarity > best_score:
                    best_score = similarity
                    best_customer_id = customer['id']
                    best_match = customer['name']
            else:
                # For individual names, use lower threshold
                if similarity > 0.7 and similarity > best_score:
                    best_score = similarity
                    best_customer_id = customer['id']
                    best_match = customer['name']

        # Strategy 4: Check for known variations
        known_matches = self.check_known_variations(normalized_invoice)
        if known_matches:
            return known_matches, 'known_variation', 0.95

        if best_customer_id:
            if best_score >= 0.9:
                return best_customer_id, 'fuzzy_high', best_score
            elif best_score >= 0.7:
                return best_customer_id, 'fuzzy_medium', best_score
            else:
                return best_customer_id, 'fuzzy_low', best_score

        return None, 'no_match', 0.0

    def is_company_name(self, name: str) -> bool:
        """Check if name appears to be a company."""
        company_indicators = [
            'council', 'university', 'school', 'group', 'entertainment',
            'pty', 'ltd', 'collective', 'accounts payable', 'club'
        ]
        return any(indicator in name.lower() for indicator in company_indicators)

    def check_known_variations(self, invoice_name: str) -> Optional[str]:
        """Check for known customer name variations."""
        known_variations = {
            'housing first': 'Housing First (Emma)',
            'housing first (ruby)': 'Housing First (Emma)',
            'banyule city council': 'Banyule City Council',
            'kate - virtue vice': 'Kate - Virtue & Vice',
            'kate virtue & vice': 'Kate - Virtue & Vice',
            'la trobe university': 'La Trobe University',
            'eat the beat': 'Eat The Beat'
        }

        normalized = invoice_name.lower().strip()
        if normalized in known_variations:
            # Find the customer ID for this known variation
            target_name = known_variations[normalized]
            for customer in self.customers:
                if customer['name'] == target_name:
                    return customer['id']

        return None

    def process_invoices(self):
        """Process and import invoice data."""
        logger.info("Starting enhanced invoice import process...")

        # Load invoice data
        try:
            df = pd.read_csv('cleaned_invoices.csv')

            # Clean column names (remove Excel formula format)
            df.columns = [col.replace('="', '').replace('"', '') for col in df.columns]

            logger.info(f"Loaded {len(df)} invoice records")
            logger.info(f"Columns: {df.columns.tolist()}")
        except Exception as e:
            logger.error(f"Failed to load invoice data: {str(e)}")
            return False

        # Process each invoice
        successful_imports = 0
        manual_review_cases = []

        for index, row in df.iterrows():
            try:
                # Get customer name and clean it
                customer_name = str(row.get('Customer', '')).strip()
                if not customer_name or customer_name == 'nan':
                    logger.info(f"Skipping row {index}: No customer name")
                    continue

                # Clean the customer name (remove Excel formula quotes)
                customer_name = customer_name.replace('="', '').replace('"', '')
                customer_name = customer_name.strip()

                if not customer_name:
                    logger.info(f"Skipping row {index}: Empty customer name after cleaning")
                    continue

                logger.info(f"Processing invoice for customer: '{customer_name}'")

                # Find customer match
                customer_id, match_type, confidence = self.find_customer_match(customer_name)

                # Track match results
                if match_type in ['exact_match', 'email_exact', 'known_variation']:
                    self.match_results['exact_matches'] += 1
                elif match_type.startswith('fuzzy'):
                    self.match_results['fuzzy_matches'] += 1
                elif match_type == 'no_match':
                    self.match_results['no_match'] += 1

                # Prepare invoice record
                invoice_record = self.prepare_invoice_record(row, customer_id, customer_name, match_type, confidence)

                if customer_id and confidence >= 0.8:
                    # High confidence match - import directly
                    if self.import_invoice(invoice_record):
                        successful_imports += 1
                        logger.info(f"Imported invoice {row.get('Invoice number')} for {customer_name} ({match_type}, {confidence:.2f})")
                else:
                    # Low confidence or no match - add to manual review
                    manual_review_cases.append({
                        'invoice_data': invoice_record,
                        'customer_name': customer_name,
                        'match_type': match_type,
                        'confidence': confidence,
                        'suggested_customer_id': customer_id
                    })
                    self.match_results['manual_review'] += 1

            except Exception as e:
                logger.error(f"Error processing invoice {index}: {str(e)}")

        # Save manual review cases
        if manual_review_cases:
            self.save_manual_review_cases(manual_review_cases)

        # Generate report
        self.generate_import_report(successful_imports, len(df), manual_review_cases)

        return True

    def prepare_invoice_record(self, row, customer_id: Optional[str], customer_name: str,
                             match_type: str, confidence: float) -> Dict:
        """Prepare invoice record for import."""
        # Parse amounts
        subtotal = self.parse_amount(row.get('Subtotal'))
        discount = self.parse_amount(row.get('Discount')) or 0
        taxes = self.parse_amount(row.get('Taxes')) or 0
        total = self.parse_amount(row.get('Total'))

        # Calculate amount (main amount field)
        amount = total if total is not None else (subtotal or 0) + taxes - discount

        # Create a dummy order to satisfy the database constraint
        order_id = self.create_dummy_order(customer_id, amount, row) if customer_id else None

        record = {
            'invoice_number': self.clean_field(row.get('Invoice number')),
            'customer_id': customer_id,
            'order_id': order_id,
            'amount': amount,
            'currency': self.clean_field(row.get('Currency')) or 'AUD',
            'issue_date': self.parse_date(row.get('Date issued')),
            'due_date': self.parse_date(row.get('Due Date')),
            'status': self.map_invoice_status(self.clean_field(row.get('Invoice status'))),
            'tax_amount': taxes,
            'notes': f'Migrated from Wix. Original customer: {customer_name}. Match: {match_type} ({confidence:.2f})'
        }

        # Remove None values except for customer_id (which can be None for manual review)
        return {k: v for k, v in record.items() if v is not None or k == 'customer_id'}

    def clean_field(self, value) -> Optional[str]:
        """Clean text field."""
        if pd.isna(value) or value == '' or str(value) == 'nan':
            return None

        # Clean Excel formula format
        cleaned = str(value).strip()
        cleaned = cleaned.replace('="', '').replace('"', '')
        cleaned = cleaned.strip()

        return cleaned if cleaned else None

    def parse_date(self, date_str) -> Optional[str]:
        """Parse date string to ISO format."""
        if pd.isna(date_str) or date_str == '' or str(date_str) == 'nan':
            return None

        try:
            # Clean the date string first
            cleaned_date = self.clean_field(date_str)
            if not cleaned_date:
                return None

            # Try different date formats
            for fmt in ['%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%d', '%m-%d-%Y']:
                try:
                    dt = datetime.strptime(cleaned_date, fmt)
                    return dt.date().isoformat()
                except ValueError:
                    continue

            logger.warning(f"Could not parse date: '{cleaned_date}'")
            return None
        except Exception as e:
            logger.warning(f"Date parsing error for '{date_str}': {str(e)}")
            return None

    def parse_amount(self, amount) -> Optional[float]:
        """Parse amount to float."""
        if pd.isna(amount) or amount == '':
            return None

        try:
            return float(amount)
        except (ValueError, TypeError):
            return None

    def map_invoice_status(self, status: Optional[str]) -> str:
        """Map Wix invoice status to database status."""
        if not status:
            return 'draft'

        status_mapping = {
            'sent': 'sent',
            'paid': 'paid',
            'overdue': 'overdue',
            'draft': 'draft',
            'void': 'void',
            'refunded': 'refunded'
        }

        return status_mapping.get(status.lower(), 'draft')

    def create_dummy_order(self, customer_id: str, amount: float, row: Dict) -> Optional[str]:
        """Create a dummy order to satisfy the database constraint."""
        try:
            order_record = {
                'id': str(uuid.uuid4()),
                'customer_id': customer_id,
                'order_date': self.parse_date(row.get('Date issued')) or datetime.now().date().isoformat(),
                'status': 'completed',  # Since invoice exists, order is completed
                'payment_status': 'paid' if self.map_invoice_status(self.clean_field(row.get('Invoice status'))) == 'paid' else 'pending',
                'subtotal': amount,
                'tax': self.parse_amount(row.get('Taxes')) or 0,
                'discount': self.parse_amount(row.get('Discount')) or 0,
                'total': amount,
                'notes': f'Auto-created for Wix invoice migration: {self.clean_field(row.get("Invoice number"))}'
            }

            response = requests.post(
                f"{self.supabase_url}/rest/v1/orders",
                headers=self.headers,
                json=order_record
            )

            if response.status_code in [200, 201]:
                logger.debug(f"Created dummy order {order_record['id']} for invoice {row.get('Invoice number')}")
                return order_record['id']
            else:
                logger.warning(f"Failed to create dummy order: {response.text}")
                return None

        except Exception as e:
            logger.warning(f"Error creating dummy order: {str(e)}")
            return None

    def import_invoice(self, invoice_record: Dict) -> bool:
        """Import single invoice to database."""
        try:
            response = requests.post(
                f"{self.supabase_url}/rest/v1/invoices",
                headers=self.headers,
                json=invoice_record
            )

            if response.status_code in [200, 201]:
                return True
            else:
                logger.error(f"Failed to import invoice: {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error importing invoice: {str(e)}")
            return False

    def save_manual_review_cases(self, cases: List[Dict]):
        """Save manual review cases to file."""
        try:
            with open('manual-review-invoices.json', 'w') as f:
                json.dump(cases, f, indent=2, default=str)
            logger.info(f"Saved {len(cases)} cases for manual review")
        except Exception as e:
            logger.error(f"Error saving manual review cases: {str(e)}")

    def generate_import_report(self, successful: int, total: int, manual_cases: List[Dict]):
        """Generate comprehensive import report."""
        report = f"""
# Enhanced Invoice Import Report
**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Status:** COMPLETED

## Import Summary
- **Total invoices processed:** {total}
- **Successfully imported:** {successful}
- **Manual review required:** {len(manual_cases)}
- **Import success rate:** {(successful/total*100):.1f}%

## Matching Results
- **Exact matches:** {self.match_results['exact_matches']}
- **Fuzzy matches:** {self.match_results['fuzzy_matches']}
- **Manual review:** {self.match_results['manual_review']}
- **No matches:** {self.match_results['no_match']}

## Manual Review Cases
{len(manual_cases)} invoices require manual review due to low confidence matches.
See: manual-review-invoices.json

## Next Steps
1. Review manual cases in manual-review-invoices.json
2. Create missing customers if needed
3. Re-run import for manual review cases
4. Validate financial data accuracy
"""

        with open('enhanced-invoice-import-report.md', 'w') as f:
            f.write(report)

        logger.info("Generated comprehensive import report")
        print(report)

def main():
    """Main execution function."""
    importer = EnhancedInvoiceImporter()

    # Load customers
    if not importer.load_customers():
        logger.error("Failed to load customers. Aborting.")
        return False

    # Process invoices
    if not importer.process_invoices():
        logger.error("Invoice processing failed.")
        return False

    logger.info("Enhanced invoice import completed successfully!")
    return True

if __name__ == "__main__":
    main()
